// 聊天记录数据
let chats = [];

// 当前打开的聊天ID
let currentChatId = null;
let currentChatObject = null; // 存储当前聊天对象

// 下拉刷新实例
let chatPullToRefresh = null;

// 未读消息相关变量
let unreadMessages = [];
let lastReadMessageId = null;

// 表情符号数组
const emojis = [
    '😀', '😁', '😂', '🤣', '😃', '😄', '😅', '😆', 
    '😉', '😊', '😋', '😎', '😍', '😘', '🥰', '😗', 
    '😙', '😚', '🙂', '🤗', '🤩', '🤔', '🤨', '😐', 
    '😑', '😶', '🙄', '😏', '😣', '😥', '😮', '🤐',
    '😯', '😪', '😫', '🥱', '😴', '😌', '😛', '😜',
    '😝', '🤤', '😒', '😓', '😔', '😕', '🙃', '🤑',
    '😲', '☹️', '🙁', '😖', '😞', '😟', '😤', '😢',
    '😭', '😦', '😧', '😨', '😩', '🤯', '😬', '😰'
];

// 初始化聊天页面
function initChat() {
    try {

    // 从服务器获取聊天列表
    fetchChats();

    // 初始化下拉刷新功能
    initChatPullToRefresh();

    // 初始化聊天页面特定的WebSocket事件监听
    initChatPageWebSocketListeners();

    // 聊天输入框
    const chatInput = document.getElementById('chat-input');
    if (chatInput) {
        chatInput.addEventListener('keydown', handleChatInput);
        chatInput.addEventListener('input', updateSendButtonState);
        // 添加输入状态监听
        chatInput.addEventListener('input', handleTypingStatus);
        chatInput.addEventListener('blur', () => handleTypingStatus(null, false));
    }
    
    // 发送按钮
    const sendButton = document.getElementById('send-button');
    if (sendButton) {
        // 移除可能存在的旧事件监听器
        sendButton.removeEventListener('click', handleSendButtonClick);
        // 添加新的事件监听器
        sendButton.addEventListener('click', handleSendButtonClick);
    }
    
    // 返回按钮
    const backToChat = document.getElementById('back-to-chat');
    if (backToChat) {
        backToChat.addEventListener('click', hideChatDetail);
    }
    
        // 表情按钮事件绑定（确保DOM已加载）
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                setTimeout(() => {
                    bindEmojiButtonEvent();
                }, 100);
            });
        } else {
            // DOM已加载完成，直接绑定
            setTimeout(() => {
                bindEmojiButtonEvent();
            }, 100);
        }

        // 媒体和文件按钮事件绑定（延迟绑定，避免重复）
        setTimeout(() => {
            bindMediaAndFileButtons();
        }, 100);
        
        // 评论表情按钮
        const commentEmojiBtn = document.getElementById('comment-emoji-btn');
        if (commentEmojiBtn) {
            commentEmojiBtn.addEventListener('click', toggleCommentEmojiPicker);
        }
        
        // 评论取消按钮
        const cancelCommentBtn = document.getElementById('cancel-comment');
        if (cancelCommentBtn) {
            cancelCommentBtn.addEventListener('click', hideCommentBar);
    }
    
    // 媒体上传
    const mediaUpload = document.getElementById('media-upload');
    if (mediaUpload) {
        mediaUpload.addEventListener('change', handleMediaUpload);
    }

    // 文件上传
    const fileUpload = document.getElementById('file-upload');
    if (fileUpload) {
        fileUpload.addEventListener('change', handleFileUpload);
    }

    // 未读消息提示条点击事件
    const unreadIndicator = document.getElementById('unread-indicator');
    if (unreadIndicator) {
        unreadIndicator.addEventListener('click', scrollToFirstUnreadMessage);
    }

    const unreadScrollBtn = document.getElementById('unread-scroll-btn');
    if (unreadScrollBtn) {
        unreadScrollBtn.addEventListener('click', scrollToFirstUnreadMessage);
    }
    

        
        // 初始化表情选择器内容
        initEmojiPicker();
        initCommentEmojiPicker();

        // 初始化聊天消息滚动监听
        initChatScrollListener();

        // 初始化媒体预览事件委托
        initMediaPreviewDelegation();

    // 初始化点击外部关闭表情选择器
    document.addEventListener('click', (e) => {
        const emojiPicker = document.getElementById('emoji-picker');
        const emojiBtn = e.target.closest('#emoji-btn') || e.target.closest('.emoji-btn');

        if (emojiPicker && emojiPicker.classList.contains('active') &&
                !emojiPicker.contains(e.target) && !emojiBtn) {
            hideEmojiPicker();
        }

        const commentEmojiPicker = document.getElementById('comment-emoji-picker');
        const commentEmojiButton = document.getElementById('comment-emoji-btn');

        if (commentEmojiPicker && commentEmojiPicker.classList.contains('active') &&
            !commentEmojiPicker.contains(e.target) &&
            (commentEmojiButton && !commentEmojiButton.contains(e.target))) {
            commentEmojiPicker.classList.remove('active');
        }
    });
    // 确保关键函数被导出到全局作用域
    window.startNewChat = startNewChat;
    window.showChatDetail = showChatDetail;
    window.chats = chats;

    console.log('聊天模块初始化完成，关键函数已导出');
    console.log('startNewChat 函数类型:', typeof window.startNewChat);

    // 检查是否有待处理的聊天联系人
    const pendingContact = sessionStorage.getItem('pendingChatContact');
    if (pendingContact) {
        try {
            const contact = JSON.parse(pendingContact);
            console.log('发现待处理的聊天联系人:', contact);
            sessionStorage.removeItem('pendingChatContact');

            // 延迟启动聊天，确保所有初始化完成
            setTimeout(() => {
                if (typeof startNewChat === 'function') {
                    startNewChat(contact.id);
                    console.log('已启动与待处理联系人的聊天');
                }
            }, 200);
        } catch (error) {
            console.error('处理待处理联系人时出错:', error);
            sessionStorage.removeItem('pendingChatContact');
        }
    }

    } catch (error) {
        console.error('初始化聊天页面时发生错误:', error);
        throw error; // 重新抛出错误，让调用者知道初始化失败
    }
}

// 初始化媒体预览事件委托
function initMediaPreviewDelegation() {
    // 使用事件委托处理动态添加的图片和视频点击事件
    const chatMessages = document.getElementById('chat-messages');
    if (!chatMessages) return;

    chatMessages.addEventListener('click', (e) => {
        // 处理图片点击
        if (e.target.classList.contains('message-image')) {
            e.preventDefault();
            e.stopPropagation();
            e.stopImmediatePropagation(); // 阻止其他监听器
            console.log('图片被点击:', e.target.src);

            // 延迟执行，确保事件处理完成
            setTimeout(() => {
                console.log('延迟执行图片预览');
                showMediaPreview(e.target.src, 'image');
            }, 50);
            return;
        }

        // 处理视频全屏按钮点击
        if (e.target.closest('.video-fullscreen-overlay')) {
            e.preventDefault();
            e.stopPropagation();
            e.stopImmediatePropagation(); // 阻止其他监听器

            const videoContainer = e.target.closest('.video-container');
            const video = videoContainer ? videoContainer.querySelector('.message-video') : null;

            if (video && video.src) {
                console.log('视频全屏被点击:', video.src);

                // 延迟执行，确保事件处理完成
                setTimeout(() => {
                    console.log('延迟执行视频预览');
                    showMediaPreview(video.src, 'video');
                }, 50);
            }
            return;
        }
    });
}

// 绑定媒体和文件按钮事件
function bindMediaAndFileButtons() {
    // 媒体按钮事件绑定
    const mediaBtn = document.getElementById('media-btn');
    const mediaUploadInput = document.getElementById('media-upload');
    if (mediaBtn && mediaUploadInput) {
        // 移除可能存在的旧事件监听器
        mediaBtn.removeEventListener('click', handleMediaButtonClick);
        // 添加新的事件监听器
        mediaBtn.addEventListener('click', handleMediaButtonClick);
    }

    // 文件按钮事件绑定
    const fileBtn = document.getElementById('file-btn');
    const fileUploadInput = document.getElementById('file-upload');
    if (fileBtn && fileUploadInput) {
        // 移除可能存在的旧事件监听器
        fileBtn.removeEventListener('click', handleFileButtonClick);
        // 添加新的事件监听器
        fileBtn.addEventListener('click', handleFileButtonClick);
    }
}

// 媒体按钮点击处理函数
function handleMediaButtonClick() {
    const mediaUploadInput = document.getElementById('media-upload');
    if (mediaUploadInput) {
        mediaUploadInput.click();
    }
}

// 文件按钮点击处理函数
function handleFileButtonClick() {
    const fileUploadInput = document.getElementById('file-upload');
    if (fileUploadInput) {
        fileUploadInput.click();
    }
}

// 绑定表情按钮事件
function bindEmojiButtonEvent() {
    try {
        const emojiBtn = document.getElementById('emoji-btn');
        if (emojiBtn) {
            // 检查是否已经绑定过事件（避免重复绑定）
            if (!emojiBtn.hasAttribute('data-emoji-bound')) {
                // 移除可能存在的旧事件监听器
                emojiBtn.removeEventListener('click', handleEmojiButtonClick);
                // 添加新的事件监听器
                emojiBtn.addEventListener('click', handleEmojiButtonClick);
                // 标记已绑定
                emojiBtn.setAttribute('data-emoji-bound', 'true');
            }

            // 确保表情选择器存在
            ensureEmojiPickerExists();
            
            return true; // 绑定成功
        } else {
            // 限制重试次数，避免无限递归
            if (!bindEmojiButtonEvent.retryCount) {
                bindEmojiButtonEvent.retryCount = 0;
            }
            
            if (bindEmojiButtonEvent.retryCount < 10) {
                bindEmojiButtonEvent.retryCount++;
                setTimeout(() => {
                    bindEmojiButtonEvent();
                }, 500);
            } else {
            }
            return false; // 绑定失败
        }
    } catch (error) {
        console.error('绑定表情按钮事件时发生错误:', error);
        return false;
    }
}

// 确保表情选择器存在
function ensureEmojiPickerExists() {
    try {
        let emojiPicker = document.getElementById('emoji-picker');
        if (!emojiPicker) {
            createEmojiPicker();

            // 验证创建是否成功
            emojiPicker = document.getElementById('emoji-picker');
            if (emojiPicker) {
                return true;
            } else {
                return false;
            }
        } else {
            return true;
        }
    } catch (error) {
        console.error('确保表情选择器存在时发生错误:', error);
        return false;
    }
}

// 表情按钮点击处理函数
function handleEmojiButtonClick(event) {
    try {
        event.preventDefault();
        event.stopPropagation();

        // 直接实现切换逻辑，避免复杂的函数调用
        const emojiPicker = document.getElementById('emoji-picker');

        if (!emojiPicker) {
            return;
        }

        // 检查当前状态
        const isCurrentlyActive = emojiPicker.classList.contains('active');

        if (isCurrentlyActive) {
            // 当前是显示状态，需要隐藏
            emojiPicker.classList.remove('active');

            // 强制隐藏
            emojiPicker.style.display = 'none';
            emojiPicker.style.visibility = 'hidden';
            emojiPicker.style.opacity = '0';
        } else {
            // 当前是隐藏状态，需要显示

            // 确保表情选择器有内容
            const emojiContent = emojiPicker.querySelector('.emoji-picker-content');
            if (!emojiContent || emojiContent.children.length === 0) {
                if (typeof initEmojiPicker === 'function') {
                    initEmojiPicker();
                }
            }

            // 隐藏其他可能打开的选择器
            const commentEmojiPicker = document.getElementById('comment-emoji-picker');
            if (commentEmojiPicker) {
                commentEmojiPicker.classList.remove('active');
            }

            // 显示表情选择器
            emojiPicker.classList.add('active');

            // 获取表情按钮的位置，用于定位表情选择器
            const emojiBtn = document.getElementById('emoji-btn');
            const btnRect = emojiBtn ? emojiBtn.getBoundingClientRect() : null;

            // 强制设置内联样式确保显示，使用固定定位
            emojiPicker.style.display = 'block';
            emojiPicker.style.visibility = 'visible';
            emojiPicker.style.opacity = '1';
            emojiPicker.style.position = 'fixed';
            emojiPicker.style.zIndex = '9999';
            emojiPicker.style.pointerEvents = 'auto';
            emojiPicker.style.maxWidth = '400px';
            emojiPicker.style.maxHeight = '300px';
            emojiPicker.style.borderRadius = '12px';
            emojiPicker.style.background = 'white';
            emojiPicker.style.border = '2px solid #007bff';
            emojiPicker.style.boxShadow = '0 4px 20px rgba(0,0,0,0.3)';

            // 根据按钮位置定位表情选择器（在按钮上方）
            if (btnRect) {
                emojiPicker.style.bottom = (window.innerHeight - btnRect.top + 10) + 'px';
                emojiPicker.style.left = btnRect.left + 'px';
                emojiPicker.style.right = 'auto';
                emojiPicker.style.top = 'auto';
            } else {
                // 备用定位：输入框上方
                emojiPicker.style.bottom = '200px';
                emojiPicker.style.left = '20px';
                emojiPicker.style.right = '20px';
                emojiPicker.style.top = 'auto';
            }



            // 确保输入框获得焦点
            const chatInput = document.getElementById('chat-input');
            if (chatInput) {
                chatInput.focus();
            }
        }

        // 如果表情选择器显示成功，确保位置正确
        const newState = emojiPicker.classList.contains('active');
        if (newState) {
            // 确保表情选择器在正确位置显示
            emojiPicker.style.position = 'fixed';
            emojiPicker.style.bottom = '150px';
            emojiPicker.style.left = '20px';
            emojiPicker.style.right = '20px';
            emojiPicker.style.top = 'auto';
            emojiPicker.style.transform = 'none';
            emojiPicker.style.width = 'auto';
            emojiPicker.style.height = 'auto';
            emojiPicker.style.background = 'white';
            emojiPicker.style.border = '1px solid #ddd';
            emojiPicker.style.borderRadius = '12px';
            emojiPicker.style.boxShadow = '0 4px 20px rgba(0,0,0,0.15)';
        }

    } catch (error) {
        console.error('表情按钮点击处理出错:', error);
    }
}

// 初始化表情选择器
function initEmojiPicker() {
    try {
        const emojiPicker = document.getElementById('emoji-picker');
        if (!emojiPicker) {
            return false;
        }
    
        const emojiContent = emojiPicker.querySelector('.emoji-picker-content');
        if (!emojiContent) {
            return false;
        }
        
        // 确保表情选择器默认隐藏
        emojiPicker.classList.remove('active');
        
        // 清空现有内容
        emojiContent.innerHTML = '';
    
        // 添加所有表情
        emojis.forEach(emoji => {
            const emojiItem = document.createElement('div');
            emojiItem.className = 'emoji-item';
            emojiItem.textContent = emoji;
            emojiItem.addEventListener('click', (e) => {
                e.stopPropagation(); // 防止事件冒泡
                insertEmoji(emoji);
                // 点击表情后关闭选择器
                hideEmojiPicker();
            });
            emojiContent.appendChild(emojiItem);
        });

        return true;
    } catch (error) {
        console.error('初始化表情选择器时发生错误:', error);
        return false;
    }
}

// 初始化评论表情选择器
function initCommentEmojiPicker() {
    const commentEmojiPicker = document.getElementById('comment-emoji-picker');
    if (!commentEmojiPicker) return;
    
    const emojiContainer = commentEmojiPicker.querySelector('.emoji-container');
    if (!emojiContainer) return;
    
    emojiContainer.innerHTML = '';
    
    emojis.forEach(emoji => {
        const emojiItem = document.createElement('div');
        emojiItem.className = 'emoji-item';
        emojiItem.textContent = emoji;
        emojiItem.addEventListener('click', () => {
            insertCommentEmoji(emoji);
        });
        emojiContainer.appendChild(emojiItem);
    });
}

// 插入表情到输入框
function insertEmoji(emoji) {
    try {
        const chatInput = document.getElementById('chat-input');
        if (!chatInput) {
            return false;
        }
        
        // 确保输入框获得焦点
        chatInput.focus();
        
        // 获取当前光标位置
        const startPos = chatInput.selectionStart || 0;
        const endPos = chatInput.selectionEnd || 0;
        const text = chatInput.value || '';
        
        // 在光标位置插入表情
        const newText = text.substring(0, startPos) + emoji + text.substring(endPos);
        chatInput.value = newText;
        
        // 设置光标位置到插入表情的后面
        const newCursorPos = startPos + emoji.length;
        chatInput.selectionStart = newCursorPos;
        chatInput.selectionEnd = newCursorPos;
        
        // 确保输入框保持焦点
        chatInput.focus();
        
        // 触发输入事件，更新发送按钮状态
        const inputEvent = new Event('input', { bubbles: true });
        chatInput.dispatchEvent(inputEvent);
        
        // 隐藏表情选择器
        hideEmojiPicker();
        

        return true;
    } catch (error) {
        console.error('插入表情时发生错误:', error);
        return false;
    }
}

// 插入表情到评论框
function insertCommentEmoji(emoji) {
    const commentContent = document.getElementById('comment-content');
    if (!commentContent) return;
    
    const startPos = commentContent.selectionStart;
    const endPos = commentContent.selectionEnd;
    const text = commentContent.value;
    
    commentContent.value = text.substring(0, startPos) + emoji + text.substring(endPos);
    
    // 设置光标位置
    commentContent.selectionStart = commentContent.selectionEnd = startPos + emoji.length;
    commentContent.focus();
    
    // 隐藏表情选择器
    const emojiPicker = document.getElementById('comment-emoji-picker');
    if (emojiPicker) {
        emojiPicker.classList.remove('active');
    }
}

// 切换表情选择器显示/隐藏
function toggleEmojiPicker(event) {
    try {
        // 阻止事件冒泡和默认行为
        if (event) {
            event.preventDefault();
            event.stopPropagation();
        }

        let emojiPicker = document.getElementById('emoji-picker');

        if (!emojiPicker) {
            if (!ensureEmojiPickerExists()) {
                return false;
            }
            emojiPicker = document.getElementById('emoji-picker');
            if (!emojiPicker) {
                return false;
            }
        }

        // 确保表情选择器有内容
        const emojiContent = emojiPicker.querySelector('.emoji-picker-content');

        if (!emojiContent || emojiContent.children.length === 0) {
            if (!initEmojiPicker()) {
                return false;
            }
        }

        // 切换显示状态
        const isCurrentlyActive = emojiPicker.classList.contains('active');

        if (isCurrentlyActive) {
            hideEmojiPicker();
        } else {
            showEmojiPicker();
        }

        return true;
    } catch (error) {
        console.error('切换表情选择器时发生错误:', error);
        console.error('错误堆栈:', error.stack);
        return false;
    }
}

// 显示表情选择器
function showEmojiPicker() {
    try {
        const emojiPicker = document.getElementById('emoji-picker');
        if (emojiPicker) {
            // 隐藏其他可能打开的选择器
            hideAllOtherPickers();
            
            emojiPicker.classList.add('active');

            // 确保输入框获得焦点（用于后续插入表情）
            const chatInput = document.getElementById('chat-input');
            if (chatInput) {
                chatInput.focus();
            }
            
            return true;
        }
        return false;
    } catch (error) {
        console.error('显示表情选择器时发生错误:', error);
        return false;
    }
}

// 隐藏表情选择器
function hideEmojiPicker() {
    try {
        const emojiPicker = document.getElementById('emoji-picker');
        if (emojiPicker) {
            emojiPicker.classList.remove('active');

            // 清除可能的内联样式
            emojiPicker.style.display = '';
            emojiPicker.style.visibility = '';
            emojiPicker.style.opacity = '';
            emojiPicker.style.position = '';
            emojiPicker.style.zIndex = '';
            emojiPicker.style.pointerEvents = '';

            return true;
        }
        return false;
    } catch (error) {
        console.error('隐藏表情选择器时发生错误:', error);
        return false;
    }
}

// 隐藏所有其他选择器
function hideAllOtherPickers() {
    try {
        // 隐藏评论表情选择器
        const commentEmojiPicker = document.getElementById('comment-emoji-picker');
        if (commentEmojiPicker) {
            commentEmojiPicker.classList.remove('active');
        }
        
        // 可以在这里添加其他选择器的隐藏逻辑
    } catch (error) {
        console.error('隐藏其他选择器时发生错误:', error);
    }
}

// 创建表情选择器（如果不存在）
function createEmojiPicker() {
    try {
        const chatInputArea = document.querySelector('.chat-input-area');
        if (!chatInputArea) {
            return;
        }

        // 检查是否已存在
        let existingPicker = document.getElementById('emoji-picker');
        if (existingPicker) {
            return;
        }

        // 创建emoji-picker元素
        const emojiPicker = document.createElement('div');
        emojiPicker.id = 'emoji-picker';
        emojiPicker.className = 'emoji-picker';

        // 创建头部
        const emojiHeader = document.createElement('div');
        emojiHeader.className = 'emoji-picker-header';
        emojiHeader.innerHTML = `
            <div class="emoji-categories">
                <button class="emoji-category active" data-category="recent">
                    <i class="fas fa-clock"></i>
                </button>
                <button class="emoji-category" data-category="smileys">
                    <i class="fas fa-smile"></i>
                </button>
                <button class="emoji-category" data-category="people">
                    <i class="fas fa-user"></i>
                </button>
                <button class="emoji-category" data-category="nature">
                    <i class="fas fa-leaf"></i>
                </button>
                <button class="emoji-category" data-category="food">
                    <i class="fas fa-apple-alt"></i>
                </button>
                <button class="emoji-category" data-category="activities">
                    <i class="fas fa-futbol"></i>
                </button>
                <button class="emoji-category" data-category="travel">
                    <i class="fas fa-car"></i>
                </button>
                <button class="emoji-category" data-category="objects">
                    <i class="fas fa-lightbulb"></i>
                </button>
            </div>
        `;

        // 创建内容区域
        const emojiContent = document.createElement('div');
        emojiContent.className = 'emoji-picker-content';

        // 组合结构
        emojiPicker.appendChild(emojiHeader);
        emojiPicker.appendChild(emojiContent);

        // 添加到DOM
        chatInputArea.appendChild(emojiPicker);

        // 初始化内容
        initEmojiPicker();
    } catch (error) {
        console.error('创建表情选择器时发生错误:', error);
    }
}

// 切换评论表情选择器显示/隐藏
function toggleCommentEmojiPicker(event) {
    event.stopPropagation(); // 阻止事件冒泡
    const emojiPicker = document.getElementById('comment-emoji-picker');
    if (emojiPicker) {
    emojiPicker.classList.toggle('active');
    }
}

// 隐藏评论栏
function hideCommentBar() {
    const commentBar = document.querySelector('.comment-bar');
    if (commentBar) {
        commentBar.style.display = 'none';
        
        // 清空评论内容
        const commentContent = document.getElementById('comment-content');
        if (commentContent) {
            commentContent.value = '';
        }
        
        // 隐藏表情选择器
        const emojiPicker = document.getElementById('comment-emoji-picker');
        if (emojiPicker) {
            emojiPicker.classList.remove('active');
        }
    }
}

// 处理媒体上传
function handleMediaUpload(e) {
    const file = e.target.files[0];
    if (!file || !currentChatId) return;

    // 检查文件类型
    if (!file.type.startsWith('image/') && !file.type.startsWith('video/')) {
        showToast('请上传图片或视频文件', 'error');
        return;
    }

    // 文件大小限制 (500MB)
    if (file.size > 500 * 1024 * 1024) {
        showToast('文件大小不能超过500MB', 'error');
        return;
    }

    // 上传文件到服务器
    uploadFileToServer(file, file.type.startsWith('image/') ? 'image' : 'video');

    // 重置文件输入
    e.target.value = '';
}

// 处理文件上传
function handleFileUpload(e) {
    const file = e.target.files[0];
    if (!file || !currentChatId) return;

    // 文件大小限制 (500MB)
    if (file.size > 500 * 1024 * 1024) {
        showToast('文件大小不能超过500MB', 'error');
        return;
    }

    // 上传文件到服务器
    uploadFileToServer(file, 'file');

    // 重置文件输入
    e.target.value = '';
}

// 上传文件到服务器
function uploadFileToServer(file, messageType) {
    if (!currentChatId) return;

    // 创建FormData对象
    const formData = new FormData();
    formData.append('file', file);

    // 显示上传进度
    const tempId = 'temp_' + Date.now();
    addTemporaryMessage(tempId, file, messageType);

    // 上传文件
    fetch('/api/upload/file', {
        method: 'POST',
        credentials: 'include',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 0) {
            // 文件上传成功，发送消息
            const fileInfo = data.data;
            sendFileMessage(currentChatId, fileInfo, tempId);
        } else {
            // 上传失败
            showToast(data.msg || '文件上传失败', 'error');
            removeTemporaryMessage(tempId);
        }
    })
    .catch(error => {
        console.error('文件上传错误:', error);
        showToast('网络错误，文件上传失败', 'error');
        removeTemporaryMessage(tempId);
    });
}

// 添加临时消息（上传中状态）
function addTemporaryMessage(tempId, file, messageType) {
    const chatMessages = document.getElementById('chat-messages');
    if (!chatMessages) return;

    // 如果是第一条消息，清空空消息提示
    if (chatMessages.querySelector('.empty-messages')) {
        chatMessages.innerHTML = '';
    }

    const messageElement = document.createElement('div');
    messageElement.className = 'message sent';
    messageElement.dataset.tempId = tempId;
    // 添加文件名和消息类型作为数据属性，用于后续匹配
    messageElement.dataset.fileName = file.name;
    messageElement.dataset.messageType = messageType;

    // 头像
    const avatarElement = document.createElement('div');
    avatarElement.className = 'message-avatar';

    // 获取当前用户信息
    let currentUserInfo = null;
    try {
        currentUserInfo = JSON.parse(localStorage.getItem('wechat_user'));
        if (currentUserInfo && currentUserInfo.avatar) {
            avatarElement.style.backgroundImage = `url(${currentUserInfo.avatar})`;
        } else {
            avatarElement.style.backgroundImage = 'url(https://picsum.photos/seed/current_user/100/100)';
        }
    } catch (e) {
        avatarElement.style.backgroundImage = 'url(https://picsum.photos/seed/current_user/100/100)';
    }

    // 消息内容
    const messageContent = document.createElement('div');
    messageContent.className = 'message-content';

    if (messageType === 'image') {
        const reader = new FileReader();
        reader.onload = function(e) {
            const img = document.createElement('img');
            img.className = 'message-image uploading';
            img.src = e.target.result;
            img.alt = file.name; // 添加alt属性，包含文件名
            messageContent.appendChild(img);
        };
        reader.readAsDataURL(file);
    } else if (messageType === 'video') {
        const reader = new FileReader();
        reader.onload = function(e) {
            const video = document.createElement('video');
            video.className = 'message-video uploading';
            video.src = e.target.result;
            video.controls = true;
            messageContent.appendChild(video);
        };
        reader.readAsDataURL(file);
    } else {
        // 文件类型
        const fileElement = document.createElement('div');
        fileElement.className = 'file-info uploading';
        fileElement.innerHTML = `
            <div class="file-icon">
                <i class="fas fa-file"></i>
            </div>
            <div class="file-details">
                <div class="file-name">${file.name}</div>
                <div class="file-size">${formatFileSize(file.size)}</div>
            </div>
        `;
        messageContent.appendChild(fileElement);
    }

    // 上传状态
    const statusElement = document.createElement('div');
    statusElement.className = 'message-status uploading';
    statusElement.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
    statusElement.title = '上传中...';

    // 时间
    const timeElement = document.createElement('div');
    timeElement.className = 'message-time';
    timeElement.textContent = '上传中...';

    messageElement.appendChild(avatarElement);
    messageElement.appendChild(messageContent);
    messageElement.appendChild(statusElement);
    messageElement.appendChild(timeElement);

    chatMessages.appendChild(messageElement);
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

// 移除临时消息
function removeTemporaryMessage(tempId) {
    const tempMessage = document.querySelector(`[data-temp-id="${tempId}"]`);
    if (tempMessage) {
        tempMessage.remove();
    }
}

// 发送文件消息
function sendFileMessage(receiverId, fileInfo, tempId) {
    // 获取当前聊天对象
    const chat = currentChatObject;
    if (!chat) {
        console.error('当前聊天对象不存在');
        return;
    }

    // 检查是否为群聊
    const isGroupChat = chat && chat.type === 'group';
    
    let apiUrl, requestData;
    
    if (isGroupChat) {
        // 群聊文件消息
        apiUrl = `/api/group_messages/${receiverId}`;
        const formData = new FormData();
        formData.append('content', fileInfo.file_name);
        formData.append('message_type', fileInfo.file_type);
        formData.append('file_url', fileInfo.file_url);
        formData.append('file_name', fileInfo.file_name);
        formData.append('file_size', fileInfo.file_size);
        requestData = formData;
    } else {
        // 个人聊天文件消息
        apiUrl = '/api/messages/send';
        requestData = JSON.stringify({
            receiver_id: receiverId,
            content: fileInfo.file_name,
            message_type: fileInfo.file_type,
            file_url: fileInfo.file_url,
            file_name: fileInfo.file_name,
            file_size: fileInfo.file_size
        });
    }

    const fetchOptions = {
        method: 'POST',
        credentials: 'include'
    };
    
    if (isGroupChat) {
        fetchOptions.body = requestData;
    } else {
        fetchOptions.headers = { 'Content-Type': 'application/json' };
        fetchOptions.body = requestData;
    }

    fetch(apiUrl, fetchOptions)
    .then(response => response.json())
    .then(data => {
        const tempMessageElement = document.querySelector(`[data-temp-id="${tempId}"]`);

        if (data.code === 0) {
            // 发送成功，更新临时消息
            if (tempMessageElement) {
                const statusElement = tempMessageElement.querySelector('.message-status');
                const timeElement = tempMessageElement.querySelector('.message-time');

                if (statusElement) {
                    statusElement.className = 'message-status sent';
                    statusElement.innerHTML = '<i class="fas fa-check"></i>';
                    statusElement.title = '已发送';
                }

                if (timeElement) {
                    timeElement.textContent = data.data.time;
                }

                // 移除上传中的样式
                const uploadingElements = tempMessageElement.querySelectorAll('.uploading');
                uploadingElements.forEach(el => el.classList.remove('uploading'));

                tempMessageElement.dataset.messageId = data.data.id;
                tempMessageElement.removeAttribute('data-temp-id');
            }

            // 更新聊天列表
            let contentDesc = '';
            switch (fileInfo.file_type) {
                case 'image':
                    contentDesc = '[图片]';
                    break;
                case 'video':
                    contentDesc = '[视频]';
                    break;
                default:
                    contentDesc = `[文件] ${fileInfo.file_name}`;
            }
            updateChatInList(receiverId, contentDesc);
        } else {
            // 发送失败
            if (tempMessageElement) {
                const statusElement = tempMessageElement.querySelector('.message-status');
                if (statusElement) {
                    statusElement.className = 'message-status failed';
                    statusElement.innerHTML = '<i class="fas fa-exclamation-triangle"></i>';
                    statusElement.title = '发送失败，点击重试';
                    statusElement.onclick = () => sendFileMessage(receiverId, fileInfo, tempId);
                }
            }
            showToast(data.msg || '消息发送失败', 'error');
        }
    })
    .catch(error => {
        console.error('发送文件消息错误:', error);
        const tempMessageElement = document.querySelector(`[data-temp-id="${tempId}"]`);
        if (tempMessageElement) {
            const statusElement = tempMessageElement.querySelector('.message-status');
            if (statusElement) {
                statusElement.className = 'message-status failed';
                statusElement.innerHTML = '<i class="fas fa-wifi"></i>';
                statusElement.title = '网络错误，点击重试';
                statusElement.onclick = () => sendFileMessage(receiverId, fileInfo, tempId);
            }
        }
        showToast('网络错误，消息发送失败', 'error');
    });
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 创建媒体预览元素
function createMediaPreview(type, src) {
    if (type === 'image') {
        const img = document.createElement('img');
        img.className = 'message-image';
        img.src = src;
        img.style.cursor = 'pointer';
        // 使用事件委托，不在这里添加事件监听器
        return img;
    } else {
        const videoContainer = document.createElement('div');
        videoContainer.className = 'video-container';
        videoContainer.style.position = 'relative';
        videoContainer.style.display = 'inline-block';

        const video = document.createElement('video');
        video.className = 'message-video';
        video.src = src;
        video.controls = true;

        // 创建全屏按钮
        const fullscreenBtn = document.createElement('div');
        fullscreenBtn.className = 'video-fullscreen-overlay';
        fullscreenBtn.style.cssText = `
            position: absolute;
            top: 10px;
            right: 10px;
            width: 40px;
            height: 40px;
            background-color: rgba(0,0,0,0.6);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            z-index: 10;
            transition: all 0.3s ease;
        `;
        fullscreenBtn.innerHTML = '<i class="fas fa-expand" style="color: white; font-size: 16px;"></i>';
        fullscreenBtn.title = '全屏观看';

        // 悬停效果
        fullscreenBtn.addEventListener('mouseenter', () => {
            fullscreenBtn.style.backgroundColor = 'rgba(0,0,0,0.8)';
            fullscreenBtn.style.transform = 'scale(1.1)';
        });

        fullscreenBtn.addEventListener('mouseleave', () => {
            fullscreenBtn.style.backgroundColor = 'rgba(0,0,0,0.6)';
            fullscreenBtn.style.transform = 'scale(1)';
        });

        videoContainer.appendChild(video);
        videoContainer.appendChild(fullscreenBtn);
        return videoContainer;
    }
}

// 显示媒体预览
function showMediaPreview(src, type) {
    try {
        console.log('显示媒体预览:', src, type);

        if (!src) {
            console.error('媒体源为空');
            return;
        }

        if (type === 'image') {
            // 使用预定义的图片查看器
            const imageViewerModal = document.getElementById('image-viewer-modal');
            const imageContainer = document.getElementById('image-container');
            
            if (!imageViewerModal || !imageContainer) {
                console.error('找不到图片查看器元素');
                return;
            }
            
            // 清空图片容器
            imageContainer.innerHTML = '';
            
            // 创建新图片元素
            const img = document.createElement('img');
            img.src = src;
            
            // 添加图片加载事件
            img.onload = function() {
                // 添加淡入动画
                setTimeout(() => {
                    imageContainer.style.opacity = '1';
                    imageContainer.style.transform = 'scale(1)';
                }, 50);
            };
            
            // 添加图片
            imageContainer.appendChild(img);
            
            // 显示弹窗
            imageViewerModal.style.display = 'flex';
            
            // 添加active类来触发CSS过渡效果
            setTimeout(() => {
                imageViewerModal.classList.add('active');
            }, 10);
            
            // 更新导航 - 单张图片时隐藏导航
            const imageNavigation = document.querySelector('.image-navigation');
            if (imageNavigation) {
                imageNavigation.style.display = 'none';
            }
            
        } else if (type === 'video') {
            // 使用预定义的视频查看器
            const videoViewerModal = document.getElementById('video-viewer-modal');
            const videoPlayer = document.getElementById('video-player');
            
            if (!videoViewerModal || !videoPlayer) {
                console.error('找不到视频查看器元素');
                return;
            }
            
            // 设置视频源
            videoPlayer.src = src;
            
            // 显示弹窗
            videoViewerModal.style.display = 'flex';
            
            // 添加active类来触发CSS过渡效果
            setTimeout(() => {
                videoViewerModal.classList.add('active');
            }, 10);
            
            // 播放视频
            videoPlayer.play();
        }

    } catch (error) {
        console.error('显示媒体预览时出错:', error);
        // 确保页面不会卡住
        document.body.style.overflow = '';
    }
}

// 更新发送按钮状态
function updateSendButtonState() {
    const chatInput = document.getElementById('chat-input');
    const sendButton = document.getElementById('send-button');

    if (!chatInput || !sendButton) {
        return;
    }

    const hasContent = chatInput.value.trim().length > 0;
    const hasChat = currentChatId !== null;

    if (hasContent && hasChat) {
        sendButton.disabled = false;
        sendButton.style.opacity = '1';
        sendButton.style.cursor = 'pointer';
        sendButton.style.pointerEvents = 'auto';
    } else {
        sendButton.disabled = true;
        sendButton.style.opacity = '0.5';
        sendButton.style.cursor = 'not-allowed';
        sendButton.style.pointerEvents = 'auto';
    }
}

// 处理发送按钮点击
function handleSendButtonClick(e) {
    e.preventDefault();
    e.stopPropagation();

    const chatInput = document.getElementById('chat-input');
    if (!chatInput) {
        return;
    }

    const messageContent = chatInput.value.trim();
    if (!messageContent) {
        return;
    }

    if (!currentChatId) {
        return;
    }

    // 清空输入框
    chatInput.value = '';

    // 更新发送按钮状态
    updateSendButtonState();

    // 发送消息到服务器
    sendMessage(currentChatId, messageContent);
}

// 从服务器获取聊天列表
function fetchChats() {
    return fetch('/api/chats', {
        credentials: 'include'
    })
        .then(response => response.json())
        .then(data => {
            if (data.code === 0) {
                // 保存当前的前端数据（包括模拟的未读消息数和最新消息）
                const currentChats = [...chats];

                // 更新服务器数据
                chats = data.data;
                console.log('获取到聊天列表:', chats);

                // 合并前端数据，保留模拟的未读消息数和最新消息
                currentChats.forEach(currentChat => {
                    // 使用ID和类型进行精确匹配
                    const serverChat = chats.find(c => 
                        c.id === currentChat.id && 
                        c.type === currentChat.type
                    );
                    if (serverChat) {
                        // 保留前端的未读消息数（如果更大）
                        if (currentChat.unread > (serverChat.unread || 0)) {
                            serverChat.unread = currentChat.unread;
                        }

                        // 保留前端的最新消息（如果有更新的时间戳）
                        if (currentChat.lastMessage && currentChat.lastMessage !== serverChat.lastMessage) {
                            // 简单的时间比较：如果前端有更新的消息，就使用前端的数据
                            if (currentChat.time && (!serverChat.time || currentChat.time > serverChat.time)) {
                                serverChat.lastMessage = currentChat.lastMessage;
                                serverChat.time = currentChat.time;
                                console.log('保留前端消息:', {
                                    id: currentChat.id,
                                    type: currentChat.type,
                                    lastMessage: currentChat.lastMessage,
                                    time: currentChat.time
                                });
                            }
                        }
                    }
                });

                renderChats();
                // 更新底部导航栏徽标
                updateChatTabBadge();
                return data;
            } else {
                console.error('获取聊天列表失败:', data.msg);
                throw new Error(data.msg);
            }
        })
        .catch(error => {
            console.error('获取聊天列表错误:', error);
            throw error;
        });
}











// 开始新的聊天
function startNewChat(userId) {
    // 检查是否已存在该聊天
    const existingChat = chats.find(chat => chat.id === userId);

    if (existingChat) {
        showChatDetail(userId, existingChat);
        return;
    }
    
    // 获取该用户的信息和聊天记录
    fetch(`/api/messages/${userId}`, {
        credentials: 'include'
    })
        .then(response => response.json())
        .then(data => {
            if (data.code === 0) {
                // 添加到聊天列表
                const chat = {
                    id: data.data.chat.id,
                    name: data.data.chat.name,
                    avatar: data.data.chat.avatar,
                    lastMessage: '点击开始聊天',
                    time: '刚刚',
                    unread: 0,
                    type: 'individual'
                };

                chats.unshift(chat);
                renderChats();

                // 打开聊天详情 - 使用API返回的ID而不是传入的userId
                showChatDetail(data.data.chat.id, chat);
            } else {
                console.error('获取用户信息失败:', data.msg);
            }
        })
        .catch(error => {
            console.error('获取用户信息错误:', error);
        });
}

// 渲染聊天列表
function renderChats() {
    const chatList = document.getElementById('chat-list');
    if (!chatList) return;

    chatList.innerHTML = '';

    if (chats.length === 0) {
        const emptyMessage = document.createElement('div');
        emptyMessage.className = 'empty-chat-message';
        emptyMessage.textContent = '暂无聊天记录';
        chatList.appendChild(emptyMessage);
        return;
    }

    chats.forEach(chat => {
        const chatElement = createChatElement(chat);
        chatList.appendChild(chatElement);
    });

    // 显示移动端操作提示
    showMobileChatTipsIfNeeded();

    // 更新底部导航栏徽标
    updateChatTabBadge();
}

// 创建聊天列表项
function createChatElement(chat) {
    const chatItem = document.createElement('div');
    chatItem.className = 'chat-item';
    chatItem.setAttribute('data-id', chat.id);
    chatItem.setAttribute('data-type', chat.type || 'individual'); // 存储聊天类型
    chatItem.setAttribute('data-name', chat.name || '');
    chatItem.setAttribute('data-avatar', chat.avatar || '');
    
    const avatarContainer = document.createElement('div');
    avatarContainer.className = 'avatar-container';
    avatarContainer.style.position = 'relative';
    
    const avatarElement = document.createElement('div');
    avatarElement.className = 'chat-avatar';
    avatarElement.style.backgroundImage = `url(${chat.avatar})`;
    avatarContainer.appendChild(avatarElement);
    
    // 添加未读消息数徽标到头像容器
    if (chat.unread > 0) {
        const badgeElement = document.createElement('div');
        badgeElement.className = 'chat-badge';
        badgeElement.textContent = chat.unread > 99 ? '99+' : chat.unread.toString();
        avatarContainer.appendChild(badgeElement);
    }
    
    const infoElement = document.createElement('div');
    infoElement.className = 'chat-info';
    
    const topElement = document.createElement('div');
    topElement.className = 'chat-top';
    
    const nameElement = document.createElement('div');
    nameElement.className = 'chat-name';
    nameElement.textContent = chat.name;
    
    const timeElement = document.createElement('div');
    timeElement.className = 'chat-time';
    timeElement.textContent = chat.time;
    
    const messageElement = document.createElement('div');
    messageElement.className = 'chat-message';
    messageElement.textContent = chat.lastMessage;
    
    topElement.appendChild(nameElement);
    topElement.appendChild(timeElement);
    infoElement.appendChild(topElement);
    infoElement.appendChild(messageElement);
    
    chatItem.appendChild(avatarContainer);
    chatItem.appendChild(infoElement);
    
    // 点击打开聊天详情
    chatItem.addEventListener('click', () => {
        const chatId = parseInt(chatItem.getAttribute('data-id'));
        const chatType = chatItem.getAttribute('data-type');
        
        // 从聊天列表中重新查找聊天对象，优先使用类型匹配
        let targetChat = chats.find(c => c.id === chatId && c.type === chatType);
        if (!targetChat) {
            targetChat = chats.find(c => c.id === chatId);
        }
        
        showChatDetail(chatId, targetChat);
    });

    // 右键菜单
    chatItem.addEventListener('contextmenu', (e) => {
        e.preventDefault();
        showChatContextMenu(e, chat);
    });

    // 为移动端添加滑动删除功能
    addSwipeDeleteFeatureForChat(chatItem, chat);

    return chatItem;
}

// 显示聊天详情
function showChatDetail(chatId, chatObject = null) {
    currentChatId = chatId;

    // 重置未读消息状态
    unreadMessages = [];
    hideUnreadIndicator();

    // 使用传入的聊天对象，或者从列表中查找
    let currentChat = chatObject;
    if (!currentChat) {
        // 如果没有传入聊天对象，从列表中查找（保持原始查找顺序，不改变优先级）
        currentChat = chats.find(c => c.id === chatId);
    }
    
    // 设置全局当前聊天对象
    currentChatObject = currentChat;
    if (currentChat && currentChat.unread > 0) {
        currentChat.unread = 0;
        // 更新底部导航栏徽标
        updateChatTabBadge();
        // 重新渲染聊天列表以更新徽标显示
        renderChats();
    }

    const chatDetail = document.getElementById('chat-detail');
    const chatDetailName = document.getElementById('chat-detail-name');
    const chatMessages = document.getElementById('chat-messages');

    if (!chatDetail || !chatDetailName || !chatMessages) return;

    // 显示加载状态
    chatMessages.innerHTML = '<div class="loading-messages">加载聊天记录中...</div>';

    // 设置临时标题
    if (currentChat) {
        chatDetailName.textContent = currentChat.name;
    } else {
        chatDetailName.textContent = '聊天';
    }
    
    // 显示聊天详情页面
    chatDetail.classList.add('active');

    // 重新绑定所有按钮事件（确保在聊天详情页面显示后绑定）
    setTimeout(() => {
        bindEmojiButtonEvent();
        bindMediaAndFileButtons();
        updateSendButtonState();
    }, 100);
    
    // 从服务器获取聊天记录
    // 检查是否为群聊
    const isGroupChat = currentChat && currentChat.type === 'group';
    const apiUrl = isGroupChat ? `/api/group_messages/${chatId}` : `/api/messages/${chatId}`;
    
    fetch(apiUrl, {
        credentials: 'include'
    })
        .then(response => response.json())
        .then(data => {
            if (data.code === 0) {
                chatDetailName.textContent = data.data.chat.name;
                renderChatMessages(data.data.messages, data.data.chat);
                
                // 不要在这里刷新聊天列表，避免覆盖新创建的聊天
                // fetchChats();
            } else {
                chatMessages.innerHTML = `<div class="error-message">获取聊天记录失败: ${data.msg}</div>`;
                console.error('获取聊天记录失败:', data.msg);
            }
        })
        .catch(error => {
            chatMessages.innerHTML = `<div class="error-message">获取聊天记录错误: ${error.message}</div>`;
            console.error('获取聊天记录错误:', error);
        });
}

// 隐藏聊天详情
function hideChatDetail() {
    const chatDetail = document.getElementById('chat-detail');
    if (!chatDetail) return;
    
    chatDetail.classList.remove('active');
    currentChatId = null;
    currentChatObject = null; // 清除当前聊天对象
}

// 渲染聊天消息
function renderChatMessages(messages, chat) {
    const chatMessages = document.getElementById('chat-messages');
    if (!chatMessages) return;
    
    chatMessages.innerHTML = '';
    
    if (messages.length === 0) {
        const emptyMessage = document.createElement('div');
        emptyMessage.className = 'empty-messages';
        emptyMessage.textContent = '暂无聊天记录，发送消息开始聊天';
        chatMessages.appendChild(emptyMessage);
        return;
    }
    
    messages.forEach(message => {
        const messageElement = createMessageElement(message, chat);
        chatMessages.appendChild(messageElement);
    });
    
    // 滚动到底部
    chatMessages.scrollTop = chatMessages.scrollHeight;

    // 更新消息已读状态显示
    updateMessageReadStatus();

    // 如果是群聊，自动标记消息为已读
    if (chat && chat.type === 'group') {
        markGroupMessagesAsRead(chat.id, messages);
    }

    // 初始化滚动监听（如果还没有绑定）
    setTimeout(() => {
        initChatScrollListener();
        // 初始检查未读消息
        updateUnreadIndicator();
    }, 100);
}

// 创建消息元素
function createMessageElement(message, chat) {
    const messageElement = document.createElement('div');
    messageElement.className = `message ${message.type}`;
    
    const avatarElement = document.createElement('div');
    avatarElement.className = 'message-avatar';
    
    // 获取当前用户信息
    let currentUserInfo = null;
    try {
        currentUserInfo = JSON.parse(localStorage.getItem('wechat_user'));
    } catch (e) {
        console.error('获取当前用户信息失败', e);
    }
    
    if (message.type === 'received') {
        // 检查是否为群聊消息
        if (chat.type === 'group' && message.sender_avatar) {
            avatarElement.style.backgroundImage = `url(${message.sender_avatar})`;
        } else {
            avatarElement.style.backgroundImage = `url(${chat.avatar})`;
        }
    } else if (currentUserInfo && currentUserInfo.avatar) {
        avatarElement.style.backgroundImage = `url(${currentUserInfo.avatar})`;
    } else {
        avatarElement.style.backgroundImage = 'url(https://picsum.photos/seed/current_user/100/100)';
    }
    
    const messageContent = document.createElement('div');
    messageContent.className = 'message-content';

    // 如果是群聊的接收消息，显示发送者名字
    if (chat.type === 'group' && message.type === 'received' && message.sender_name) {
        const senderName = document.createElement('div');
        senderName.className = 'message-sender-name';
        senderName.textContent = message.sender_name;
        senderName.style.fontSize = '12px';
        senderName.style.color = '#666';
        senderName.style.marginBottom = '4px';
        messageContent.appendChild(senderName);
    }

    // 根据消息类型渲染不同的内容
    const messageType = message.message_type || 'text';

    if (messageType === 'image' && message.file_url) {
        const img = document.createElement('img');
        img.className = 'message-image';
        img.src = message.file_url;
        img.alt = message.file_name || '图片';
        img.style.cursor = 'pointer';
        // 不在这里添加事件监听器，使用事件委托处理
        messageContent.appendChild(img);
    } else if (messageType === 'video' && message.file_url) {
        const videoContainer = document.createElement('div');
        videoContainer.className = 'video-container';
        videoContainer.style.position = 'relative';
        videoContainer.style.display = 'inline-block';

        const video = document.createElement('video');
        video.className = 'message-video';
        video.src = message.file_url;
        video.controls = true;
        video.preload = 'metadata';

        // 创建全屏按钮覆盖层
        const fullscreenOverlay = document.createElement('div');
        fullscreenOverlay.className = 'video-fullscreen-overlay';
        fullscreenOverlay.style.position = 'absolute';
        fullscreenOverlay.style.top = '10px';
        fullscreenOverlay.style.right = '10px';
        fullscreenOverlay.style.width = '40px';
        fullscreenOverlay.style.height = '40px';
        fullscreenOverlay.style.backgroundColor = 'rgba(0,0,0,0.6)';
        fullscreenOverlay.style.borderRadius = '50%';
        fullscreenOverlay.style.display = 'flex';
        fullscreenOverlay.style.alignItems = 'center';
        fullscreenOverlay.style.justifyContent = 'center';
        fullscreenOverlay.style.cursor = 'pointer';
        fullscreenOverlay.style.zIndex = '10';
        fullscreenOverlay.style.transition = 'all 0.3s ease';
        fullscreenOverlay.innerHTML = '<i class="fas fa-expand" style="color: white; font-size: 16px;"></i>';
        fullscreenOverlay.title = '全屏观看';

        // 鼠标悬停效果
        fullscreenOverlay.addEventListener('mouseenter', () => {
            fullscreenOverlay.style.backgroundColor = 'rgba(0,0,0,0.8)';
            fullscreenOverlay.style.transform = 'scale(1.1)';
        });

        fullscreenOverlay.addEventListener('mouseleave', () => {
            fullscreenOverlay.style.backgroundColor = 'rgba(0,0,0,0.6)';
            fullscreenOverlay.style.transform = 'scale(1)';
        });

        videoContainer.appendChild(video);
        videoContainer.appendChild(fullscreenOverlay);
        messageContent.appendChild(videoContainer);
    } else if (messageType === 'file' && message.file_url) {
        const fileElement = document.createElement('div');
        fileElement.className = 'file-info';
        fileElement.innerHTML = `
            <div class="file-icon">
                <i class="fas fa-file"></i>
            </div>
            <div class="file-details">
                <div class="file-name">${message.file_name || '文件'}</div>
                <div class="file-size">${message.file_size ? formatFileSize(message.file_size) : ''}</div>
            </div>
            <div class="file-download">
                <a href="${message.file_url}" download="${message.file_name}" target="_blank">
                    <i class="fas fa-download"></i>
                </a>
            </div>
        `;
        messageContent.appendChild(fileElement);
    } else {
        // 文本消息
        const messageBubble = document.createElement('div');
        messageBubble.className = 'message-bubble';
        messageBubble.textContent = message.content || '';
        messageContent.appendChild(messageBubble);
    }
    
    messageElement.appendChild(avatarElement);
    messageElement.appendChild(messageContent);
    
    // 如果有时间显示
    if (message.time) {
        const timeElement = document.createElement('div');
        timeElement.className = 'message-time';

        // 创建时间文本元素
        const timeTextElement = document.createElement('span');
        timeTextElement.textContent = message.time;
        timeElement.appendChild(timeTextElement);
        
        // 为发送的消息添加状态指示器
        if (message.type === 'sent') {
            const statusElement = document.createElement('span');

            // 根据发送状态和已读状态确定显示内容
            if (message.status === 'sending') {
                statusElement.className = 'message-status sending';
                statusElement.innerHTML = '<span class="loading-spinner"></span>';
                statusElement.title = '发送中...';
            } else if (message.status === 'failed') {
                statusElement.className = 'message-status failed';
                statusElement.innerHTML = '<i class="fas fa-exclamation-triangle"></i>';
                statusElement.title = '发送失败，点击重试';
            } else if (chat.type === 'group') {
                // 群聊消息显示已读人数
                statusElement.className = 'message-status group-read';
                const readCount = message.read_count || 0;
                const totalMembers = message.total_members || 0;

                if (readCount > 0) {
                    statusElement.innerHTML = `<i class="fas fa-check"></i> ${readCount}人已读`;
                    statusElement.title = `${readCount}/${totalMembers} 人已读，点击查看详情`;
                    statusElement.style.cursor = 'pointer';
                    // 添加点击事件查看已读详情
                    statusElement.addEventListener('click', () => {
                        showGroupMessageReadDetails(message.id);
                    });
                } else {
                    statusElement.innerHTML = '<i class="fas fa-check"></i>';
                    statusElement.title = '已发送';
                }
            } else if (message.read === true) {
                // 个人聊天：消息已被对方读取，显示两个绿色勾勾
                statusElement.className = 'message-status read';
                statusElement.innerHTML = '<i class="fas fa-check"></i><i class="fas fa-check"></i>';
                statusElement.title = '已读';
            } else {
                // 个人聊天：消息已发送但未读，显示一个绿色勾勾
                statusElement.className = 'message-status sent';
                statusElement.innerHTML = '<i class="fas fa-check"></i>';
                statusElement.title = '已发送';
            }

            timeElement.appendChild(statusElement);
        }
        
        messageElement.appendChild(timeElement);
    }
    
    // 为消息元素添加消息ID，用于后续更新已读状态
    if (message.id) {
        messageElement.dataset.messageId = message.id;
    }

    return messageElement;
}

// 显示群聊消息已读详情
function showGroupMessageReadDetails(messageId) {
    if (!messageId) return;

    // 获取消息已读状态
    fetch(`/api/group_messages/${messageId}/read_status`, {
        credentials: 'include'
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 0) {
            const readData = data.data;
            const readUsers = readData.read_users || [];
            const readCount = readData.read_count || 0;
            const totalMembers = readData.total_members || 0;

            // 创建弹窗显示已读详情
            const modal = document.createElement('div');
            modal.className = 'group-read-modal';
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 10000;
            `;

            const content = document.createElement('div');
            content.className = 'group-read-content';
            content.style.cssText = `
                background: white;
                border-radius: 8px;
                padding: 20px;
                max-width: 400px;
                width: 90%;
                max-height: 500px;
                overflow-y: auto;
            `;

            let userListHTML = '';
            if (readUsers.length > 0) {
                userListHTML = readUsers.map(user => `
                    <div style="display: flex; align-items: center; padding: 8px 0; border-bottom: 1px solid #f0f0f0;">
                        <div style="width: 40px; height: 40px; border-radius: 50%; background-image: url(${user.avatar || 'https://picsum.photos/seed/user/100/100'}); background-size: cover; margin-right: 12px;"></div>
                        <div>
                            <div style="font-weight: 500;">${user.username}</div>
                            <div style="font-size: 12px; color: #666;">${new Date(user.read_at).toLocaleString()}</div>
                        </div>
                    </div>
                `).join('');
            } else {
                userListHTML = '<div style="text-align: center; color: #666; padding: 20px;">暂无人已读</div>';
            }

            content.innerHTML = `
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
                    <h3 style="margin: 0;">已读详情</h3>
                    <button onclick="this.closest('.group-read-modal').remove()" style="background: none; border: none; font-size: 20px; cursor: pointer;">&times;</button>
                </div>
                <div style="margin-bottom: 16px; color: #666;">
                    ${readCount}/${totalMembers} 人已读
                </div>
                <div>
                    ${userListHTML}
                </div>
            `;

            modal.appendChild(content);
            document.body.appendChild(modal);

            // 点击背景关闭弹窗
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    modal.remove();
                }
            });
        } else {
            showToast(data.msg || '获取已读详情失败', 'error');
        }
    })
    .catch(error => {
        console.error('获取已读详情错误:', error);
        showToast('网络错误，获取已读详情失败', 'error');
    });
}

// 处理聊天输入框按键事件
function handleChatInput(e) {
    if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault();
        // 创建一个模拟的点击事件来调用发送函数
        const mockEvent = {
            preventDefault: () => {},
            stopPropagation: () => {}
        };
        handleSendButtonClick(mockEvent);
    }
}

// 标记群聊消息为已读
function markGroupMessagesAsRead(groupId, messages) {
    if (!groupId || !messages || messages.length === 0) return;

    // 获取当前用户信息
    let currentUserInfo = null;
    try {
        currentUserInfo = JSON.parse(localStorage.getItem('wechat_user'));
    } catch (e) {
        console.error('获取当前用户信息失败', e);
        return;
    }

    if (!currentUserInfo) return;

    // 筛选出所有有效的消息ID（包括自己发送的和接收的）
    // 这样可以确保所有消息都能被正确标记为已读，触发WebSocket通知
    const messageIds = messages
        .filter(msg => msg.id) // 只要有ID就处理，不限制type
        .map(msg => msg.id);

    if (messageIds.length === 0) return;

    console.log('🔥 markGroupMessagesAsRead被调用:', {
        groupId,
        messagesCount: messages.length,
        messageIds,
        messages: messages.map(m => ({ id: m.id, type: m.type, content: m.content?.substring(0, 30) }))
    });

    // 发送标记已读请求
    fetch(`/api/group_messages/${groupId}/mark_read`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        credentials: 'include',
        body: JSON.stringify({
            message_ids: messageIds
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 0) {
            console.log('群聊消息已标记为已读');
        } else {
            console.error('标记群聊消息已读失败:', data.msg);
        }
    })
    .catch(error => {
        console.error('标记群聊消息已读错误:', error);
    });
}

// 发送消息到服务器
function sendMessage(receiverId, content) {
    // 生成临时消息ID
    const tempId = 'temp_' + Date.now();

    // 获取聊天对象信息 - 直接使用全局当前聊天对象
    let chat = currentChatObject;
    
    if (!chat) {
        console.error('当前聊天对象不存在');
        return;
    }
    
    // 立即添加消息到界面（显示发送中状态）
    const chatMessages = document.getElementById('chat-messages');

    if (chatMessages) {
        // 如果是第一条消息，清空空消息提示
        if (chatMessages.querySelector('.empty-messages')) {
            chatMessages.innerHTML = '';
        }

        const tempMessage = {
            id: tempId,
            type: 'sent',
            content: content,
            time: formatMessageTime(new Date()),
            status: 'sending'
        };

        const messageElement = createMessageElement(tempMessage, chat);
        messageElement.dataset.tempId = tempId;

        chatMessages.appendChild(messageElement);

        // 滚动到底部
        chatMessages.scrollTop = chatMessages.scrollHeight;

        // 隐藏未读消息提示（因为用户主动发送了消息）
        hideUnreadIndicator();
    }
    
    // 发送到服务器
    const isGroupChat = chat && chat.type === 'group';
    let apiUrl, requestBody;
    
    if (isGroupChat) {
        // 群聊消息
        apiUrl = `/api/group_messages/${receiverId}`;
        const formData = new FormData();
        formData.append('content', content);
        formData.append('message_type', 'text');
        requestBody = formData;
    } else {
        // 个人聊天消息
        apiUrl = '/api/messages/send';
        requestBody = JSON.stringify({
            receiver_id: receiverId,
            content: content
        });
    }
    
    const fetchOptions = {
        method: 'POST',
        credentials: 'include'
    };
    
    if (isGroupChat) {
        fetchOptions.body = requestBody;
    } else {
        fetchOptions.headers = { 'Content-Type': 'application/json' };
        fetchOptions.body = requestBody;
    }
    
    fetch(apiUrl, fetchOptions)
    .then(response => response.json())
    .then(data => {
        // 找到临时消息元素
        const tempMessageElement = document.querySelector(`[data-temp-id="${tempId}"]`);
        
        if (data.code === 0) {
            // 发送成功，更新消息状态
            if (tempMessageElement) {
                const statusElement = tempMessageElement.querySelector('.message-status');
                if (statusElement) {
                    // 根据聊天类型设置正确的状态类名
                    if (isGroupChat) {
                        statusElement.className = 'message-status group-read';
                        statusElement.innerHTML = '<i class="fas fa-check"></i>';
                        statusElement.title = '已发送';
                    } else {
                        statusElement.className = 'message-status sent';
                        statusElement.innerHTML = '<i class="fas fa-check"></i>';
                        statusElement.title = '已发送';
                    }
                }
                // 更新消息ID
                tempMessageElement.dataset.messageId = data.data.id;
                tempMessageElement.removeAttribute('data-temp-id');
            }
            
            // 更新聊天列表
            updateChatInList(receiverId, content);
        } else {
            // 发送失败，更新消息状态
            if (tempMessageElement) {
                const statusElement = tempMessageElement.querySelector('.message-status');
                if (statusElement) {
                    statusElement.className = 'message-status failed';
                    statusElement.innerHTML = '<i class="fas fa-exclamation-triangle"></i>';
                    statusElement.title = '点击重新发送';
                    statusElement.onclick = () => retryMessage(tempId, receiverId, content);
                }
            }

            console.error('发送消息失败:', data.msg);
            // 移除发送失败的弹出提示
            // showToast(`发送失败: ${data.msg}`, 'error');
        }
    })
    .catch(error => {
        // 网络错误，更新消息状态
        const tempMessageElement = document.querySelector(`[data-temp-id="${tempId}"]`);
        if (tempMessageElement) {
            const statusElement = tempMessageElement.querySelector('.message-status');
            if (statusElement) {
                statusElement.className = 'message-status failed';
                statusElement.innerHTML = '<i class="fas fa-wifi"></i>';
                statusElement.title = '网络错误，点击重新发送';
                statusElement.onclick = () => retryMessage(tempId, receiverId, content);
            }
        }
        
        console.error('发送消息错误:', error);
        // 移除网络错误的弹出提示
        // showToast('网络错误，消息发送失败', 'error');
    });
}

// 重试发送消息
function retryMessage(tempId, receiverId, content) {
    const messageElement = document.querySelector(`[data-temp-id="${tempId}"]`);
    if (!messageElement) return;
    
    // 更新状态为发送中
    const statusElement = messageElement.querySelector('.message-status');
    if (statusElement) {
        statusElement.className = 'message-status sending';
        statusElement.innerHTML = '<span class="loading-spinner"></span>';
        statusElement.onclick = null;
        statusElement.title = '';
    }
    
    // 重新发送
    sendMessageRetry(tempId, receiverId, content);
}

// 重新发送消息（不创建新的临时消息）
function sendMessageRetry(tempId, receiverId, content) {
    fetch('/api/messages/send', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        credentials: 'include',
        body: JSON.stringify({
            receiver_id: receiverId,
            content: content
        })
    })
    .then(response => response.json())
    .then(data => {
        const tempMessageElement = document.querySelector(`[data-temp-id="${tempId}"]`);
        
        if (data.code === 0) {
            // 发送成功
            if (tempMessageElement) {
                const statusElement = tempMessageElement.querySelector('.message-status');
                if (statusElement) {
                    statusElement.className = 'message-status sent';
                    statusElement.innerHTML = '<i class="fas fa-check"></i>';
                }
                tempMessageElement.dataset.messageId = data.data.id;
                tempMessageElement.removeAttribute('data-temp-id');
            }
            
            updateChatInList(receiverId, content);
            // 消息发送成功，不显示提示
        } else {
            // 发送失败
            if (tempMessageElement) {
                const statusElement = tempMessageElement.querySelector('.message-status');
                if (statusElement) {
                    statusElement.className = 'message-status failed';
                    statusElement.innerHTML = '<i class="fas fa-exclamation-triangle"></i>';
                    statusElement.title = '点击重新发送';
                    statusElement.onclick = () => retryMessage(tempId, receiverId, content);
                }
            }
            // 移除发送失败的弹出提示
            // showToast(`发送失败: ${data.msg}`, 'error');
        }
    })
    .catch(error => {
        const tempMessageElement = document.querySelector(`[data-temp-id="${tempId}"]`);
        if (tempMessageElement) {
            const statusElement = tempMessageElement.querySelector('.message-status');
            if (statusElement) {
                statusElement.className = 'message-status failed';
                statusElement.innerHTML = '<i class="fas fa-wifi"></i>';
                statusElement.title = '网络错误，点击重新发送';
                statusElement.onclick = () => retryMessage(tempId, receiverId, content);
            }
        }
        // 移除网络错误的弹出提示
        // showToast('网络错误，消息发送失败', 'error');
    });
}

// 更新聊天列表中的聊天
function updateChatInList(chatId, lastMessage) {
    // 查找并更新现有聊天
    const existingChatIndex = chats.findIndex(chat => chat.id === chatId);
    
    if (existingChatIndex !== -1) {
        // 更新最后一条消息和时间
        chats[existingChatIndex].lastMessage = lastMessage;
        chats[existingChatIndex].time = '刚刚';
        
        // 将该聊天移到顶部
        const chatToMove = chats.splice(existingChatIndex, 1)[0];
        chats.unshift(chatToMove);
    }
    
    // 重新渲染聊天列表
    renderChats();
}

// 更新消息已读状态显示
function updateMessageReadStatus() {
    if (!currentChatId) return;

    // 获取当前聊天的所有发送消息元素
    const sentMessages = document.querySelectorAll('.message.sent');

    sentMessages.forEach(messageElement => {
        const messageId = messageElement.dataset.messageId;
        if (!messageId) return;

        // 检查消息是否已读（这里可以通过API查询或WebSocket获取实时状态）
        // 暂时先通过定期检查实现
        checkMessageReadStatus(messageId, messageElement);
    });
}

// 检查单个消息的已读状态
function checkMessageReadStatus(messageId, messageElement) {
    // 这里可以实现API调用来检查消息是否已读
    // 为了演示，我们先实现基础逻辑
    const statusElement = messageElement.querySelector('.message-status');
    if (!statusElement) return;

    // 如果当前状态是已发送，可以通过API检查是否已读
    if (statusElement.classList.contains('sent')) {
        // 这里可以添加API调用来检查已读状态
        // fetch(`/api/messages/${messageId}/read-status`)...
    }
}

// 定期刷新聊天列表
function startChatRefresher() {
    // 每30秒刷新一次聊天列表
    setInterval(fetchChats, 30000);
}

// 初始化聊天消息滚动监听
function initChatScrollListener() {
    const chatMessages = document.getElementById('chat-messages');
    if (!chatMessages) return;

    chatMessages.addEventListener('scroll', handleChatScroll);
}

// 处理聊天消息滚动事件
function handleChatScroll() {
    const chatMessages = document.getElementById('chat-messages');
    if (!chatMessages) return;

    // 检查是否滚动到底部
    const isAtBottom = chatMessages.scrollTop + chatMessages.clientHeight >= chatMessages.scrollHeight - 10;

    if (isAtBottom) {
        // 如果滚动到底部，隐藏未读消息提示
        hideUnreadIndicator();
        // 标记所有消息为已读
        markAllMessagesAsRead();
    } else {
        // 检查并更新未读消息提示
        updateUnreadIndicator();
    }
}

// 显示未读消息提示条
function showUnreadIndicator(count) {
    const unreadIndicator = document.getElementById('unread-indicator');
    const unreadCount = document.getElementById('unread-count');

    if (!unreadIndicator || !unreadCount) return;

    unreadCount.textContent = count;
    unreadIndicator.style.display = 'flex';
}

// 隐藏未读消息提示条
function hideUnreadIndicator() {
    const unreadIndicator = document.getElementById('unread-indicator');
    if (!unreadIndicator) return;

    unreadIndicator.style.display = 'none';
}

// 滚动到第一条未读消息
function scrollToFirstUnreadMessage() {
    const chatMessages = document.getElementById('chat-messages');
    if (!chatMessages) return;

    // 滚动到底部
    chatMessages.scrollTop = chatMessages.scrollHeight;

    // 隐藏未读消息提示
    hideUnreadIndicator();
}

// 更新未读消息提示
function updateUnreadIndicator() {
    const chatMessages = document.getElementById('chat-messages');
    if (!chatMessages) return;

    // 获取所有消息
    const allMessages = chatMessages.querySelectorAll('.message');
    let unreadCount = 0;

    // 计算可见区域
    const containerTop = chatMessages.scrollTop;
    const containerBottom = containerTop + chatMessages.clientHeight;

    // 从最后一条消息开始向上检查，找到第一条在可见区域外的消息
    for (let i = allMessages.length - 1; i >= 0; i--) {
        const message = allMessages[i];
        const messageTop = message.offsetTop;
        const messageBottom = messageTop + message.offsetHeight;

        // 如果消息在可见区域下方（用户还没看到）
        if (messageTop > containerBottom) {
            unreadCount++;
        } else if (messageBottom <= containerBottom) {
            // 如果消息完全在可见区域内或上方，停止计数
            break;
        }
    }

    if (unreadCount > 0) {
        showUnreadIndicator(unreadCount);
    } else {
        hideUnreadIndicator();
    }
}

// 标记所有消息为已读
function markAllMessagesAsRead() {
    // 这里可以添加API调用来标记消息为已读
    // 暂时只处理UI状态
    unreadMessages = [];
}

// 计算总未读消息数
function calculateTotalUnreadCount() {
    let totalUnread = 0;
    chats.forEach(chat => {
        if (chat.unread && chat.unread > 0) {
            totalUnread += chat.unread;
        }
    });
    return totalUnread;
}

// 更新底部导航栏聊天标签的徽标
function updateChatTabBadge() {
    const chatTabBadge = document.getElementById('chat-tab-badge');
    if (!chatTabBadge) {
        console.warn('聊天徽标元素未找到');
        return;
    }

    const totalUnread = calculateTotalUnreadCount();
    console.log('更新聊天徽标，未读消息总数:', totalUnread);

    if (totalUnread > 0) {
        chatTabBadge.textContent = totalUnread > 99 ? '99+' : totalUnread.toString();
        chatTabBadge.style.display = 'flex';
        console.log('显示聊天徽标:', chatTabBadge.textContent);
    } else {
        chatTabBadge.style.display = 'none';
        console.log('隐藏聊天徽标');
    }
}

// 模拟接收新消息（用于测试）
function simulateNewMessage() {
    if (!currentChatId) return;

    const chatMessages = document.getElementById('chat-messages');
    if (!chatMessages) return;

    // 模拟消息内容数组
    const mockMessages = [
        '你好！',
        '今天天气不错呢',
        '你在忙什么？',
        '有空一起吃饭吗？',
        '刚才看到一个很有趣的新闻',
        '周末有什么计划？',
        '这个功能看起来很棒！',
        '测试一下未读消息提示',
        '微信风格的提示条很不错',
        '记得点击提示条可以滚动到底部哦'
    ];

    // 随机选择一条消息
    const randomContent = mockMessages[Math.floor(Math.random() * mockMessages.length)];

    // 创建一个模拟的接收消息
    const mockMessage = {
        id: Date.now(),
        type: 'received',
        content: randomContent,
        time: formatMessageTime(new Date())
    };

    // 获取当前聊天信息
    const chat = chats.find(c => c.id === currentChatId);
    if (!chat) return;

    const messageElement = createMessageElement(mockMessage, chat);
    chatMessages.appendChild(messageElement);

    // 检查用户是否在底部，如果不在则显示未读提示
    const isAtBottom = chatMessages.scrollTop + chatMessages.clientHeight >= chatMessages.scrollHeight - 10;

    // 只有当用户不在当前聊天底部时才增加未读数（模拟真实的接收消息场景）
    if (!isAtBottom) {
        if (!chat.unread) chat.unread = 0;
        chat.unread += 1;
    }

    // 更新聊天列表中的最后一条消息和时间
    chat.lastMessage = randomContent;
    chat.time = formatMessageTime(new Date());

    // 更新UI
    updateChatTabBadge();
    renderChats(); // 更新聊天列表显示

    if (!isAtBottom) {
        // 延迟一点更新未读提示，确保DOM已更新
        setTimeout(() => {
            updateUnreadIndicator();
        }, 50);
    } else {
        // 如果用户在底部，自动滚动到新消息
        setTimeout(() => {
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }, 50);
    }
}

// 页面加载完毕后启动刷新器和初始化
document.addEventListener('DOMContentLoaded', function() {
    try {

        // 初始化聊天功能
        initChat();

        // 启动聊天列表定期刷新
    startChatRefresher();

        // 确保表情选择器初始状态正确
        setTimeout(function() {
            const emojiPicker = document.getElementById('emoji-picker');
            if (emojiPicker) {
                emojiPicker.classList.remove('active');
                // 移除任何可能的内联样式，让CSS控制显示
                emojiPicker.removeAttribute('style');
            }
        }, 500);

        // 添加键盘快捷键用于测试（Ctrl+T 模拟新消息）
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.key === 't' && currentChatId) {
                e.preventDefault();
                simulateNewMessage();
                console.log('模拟新消息已发送，用于测试未读消息提示功能');
            }

            // 添加Ctrl+M快捷键来测试全局消息接收（模拟其他页面接收消息）
            if (e.ctrlKey && e.key === 'm') {
                e.preventDefault();
                testGlobalMessageReceive();
                console.log('模拟全局消息接收，用于测试徽标更新功能');
            }
        });
    } catch (error) {
        console.error('页面加载初始化时发生错误:', error);
    }
});

// 监听可能的动态内容加载
window.addEventListener('load', function() {
    setTimeout(function() {
        bindEmojiButtonEvent();
    }, 1000);
});

// 监听页面切换，确保表情按钮事件正确绑定
document.addEventListener('click', function(event) {
    // 如果点击的是聊天项，延迟绑定表情按钮事件
    if (event.target.closest('.chat-item')) {
        setTimeout(() => {
            bindEmojiButtonEvent();
            bindMediaAndFileButtons();
        }, 200);
    }

    // 点击其他地方时隐藏右键菜单
    if (!event.target.closest('.chat-context-menu')) {
        hideChatContextMenu();
    }
});



// 显示聊天右键菜单
function showChatContextMenu(event, chat) {
    // 移除现有菜单
    hideChatContextMenu();

    const menu = document.createElement('div');
    menu.className = 'chat-context-menu';
    menu.innerHTML = `
        <div class="chat-context-menu-item" onclick="deleteChatSession(${chat.id})">
            <i class="fas fa-trash"></i>
            <span>删除聊天</span>
        </div>
    `;

    // 设置菜单位置
    menu.style.left = event.pageX + 'px';
    menu.style.top = event.pageY + 'px';

    document.body.appendChild(menu);

    // 防止菜单超出屏幕
    const rect = menu.getBoundingClientRect();
    if (rect.right > window.innerWidth) {
        menu.style.left = (event.pageX - rect.width) + 'px';
    }
    if (rect.bottom > window.innerHeight) {
        menu.style.top = (event.pageY - rect.height) + 'px';
    }
}

// 隐藏聊天右键菜单
function hideChatContextMenu() {
    const existingMenu = document.querySelector('.chat-context-menu');
    if (existingMenu) {
        existingMenu.remove();
    }
}

// 删除聊天会话 - 直接删除
function deleteChatSession(chatId) {
    hideChatContextMenu();

    // 发送删除请求
    fetch(`/api/chats/${chatId}`, {
        method: 'DELETE',
        credentials: 'include'
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 0) {
            // 删除成功，从本地聊天列表中移除
            const index = chats.findIndex(c => c.id == chatId);
            if (index !== -1) {
                chats.splice(index, 1);
                renderChats();
            }

            // 如果当前正在查看被删除的聊天，返回到聊天列表
            if (currentChatId == chatId) {
                const chatDetail = document.getElementById('chat-detail');
                if (chatDetail) {
                    chatDetail.classList.remove('active');
                }
                currentChatId = null;
            }

            console.log('聊天删除成功');
        } else {
            showToast(data.msg || '删除失败，请重试', 'error');
        }
    })
    .catch(error => {
        console.error('删除聊天错误:', error);
        showToast('网络连接异常，请检查网络后重试', 'error');
    });
}

// 为聊天项添加滑动删除功能
function addSwipeDeleteFeatureForChat(chatElement, chat) {
    // 检测是否为触摸设备
    const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;

    if (!isTouchDevice) {
        return; // 非触摸设备不添加滑动功能
    }

    let startX = 0;
    let startY = 0;
    let currentX = 0;
    let isSwipeActive = false;
    let deleteButton = null;

    const SWIPE_THRESHOLD = 50; // 滑动阈值，超过此值显示删除按钮
    const DELETE_THRESHOLD = 120; // 删除阈值，超过此值直接删除
    const VERTICAL_THRESHOLD = 30; // 垂直移动阈值，超过此值取消滑动

    // 创建删除按钮
    function createDeleteButton() {
        if (deleteButton) return deleteButton;

        deleteButton = document.createElement('div');
        deleteButton.className = 'swipe-delete-button-chat';
        deleteButton.innerHTML = '删除';
        deleteButton.style.cssText = `
            position: absolute;
            right: 0;
            top: 0;
            bottom: 0;
            width: 80px;
            background: linear-gradient(135deg, #ff4757 0%, #ff3742 100%);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            transform: translateX(100%);
            transition: transform 0.3s ease;
            border-radius: 0 16px 16px 0;
            z-index: 10;
        `;

        // 点击删除按钮
        deleteButton.addEventListener('click', (e) => {
            e.stopPropagation();
            // 直接删除，不显示确认弹窗
            deleteChatSession(chat.id);
            resetSwipe();
        });

        return deleteButton;
    }

    // 重置滑动状态
    function resetSwipe() {
        if (deleteButton) {
            deleteButton.style.transform = 'translateX(100%)';
        }
        chatElement.style.transform = 'translateX(0)';
        chatElement.style.transition = 'transform 0.3s ease';
        isSwipeActive = false;

        // 移除删除按钮
        setTimeout(() => {
            if (deleteButton && deleteButton.parentNode) {
                deleteButton.parentNode.removeChild(deleteButton);
                deleteButton = null;
            }
        }, 300);
    }

    // 触摸开始
    chatElement.addEventListener('touchstart', (e) => {
        const touch = e.touches[0];
        startX = touch.clientX;
        startY = touch.clientY;
        currentX = startX;

        // 清除过渡效果，准备滑动
        chatElement.style.transition = 'none';
    }, { passive: true });

    // 触摸移动
    chatElement.addEventListener('touchmove', (e) => {
        const touch = e.touches[0];
        const deltaX = touch.clientX - startX;
        const deltaY = Math.abs(touch.clientY - startY);
        currentX = touch.clientX;

        // 如果垂直移动过多，取消滑动
        if (deltaY > VERTICAL_THRESHOLD) {
            resetSwipe();
            return;
        }

        // 只处理向左滑动
        if (deltaX < -SWIPE_THRESHOLD) {
            e.preventDefault(); // 防止页面滚动

            if (!isSwipeActive) {
                isSwipeActive = true;
                // 确保聊天项有相对定位
                chatElement.style.position = 'relative';
                chatElement.style.overflow = 'hidden';

                // 添加删除按钮
                const button = createDeleteButton();
                chatElement.appendChild(button);

                // 显示删除按钮
                setTimeout(() => {
                    button.style.transform = 'translateX(0)';
                }, 10);
            }

            // 限制滑动距离
            const maxSwipe = Math.min(Math.abs(deltaX), 100);
            chatElement.style.transform = `translateX(-${maxSwipe}px)`;

            // 如果滑动距离超过删除阈值，添加删除提示
            if (Math.abs(deltaX) > DELETE_THRESHOLD) {
                chatElement.classList.add('swipe-delete-ready-chat');
                if (navigator.vibrate) {
                    navigator.vibrate(30);
                }
            } else {
                chatElement.classList.remove('swipe-delete-ready-chat');
            }
        } else if (deltaX > 10) {
            // 向右滑动，重置状态
            resetSwipe();
        }
    }, { passive: false });

    // 触摸结束
    chatElement.addEventListener('touchend', (e) => {
        const deltaX = currentX - startX;

        // 如果滑动距离超过删除阈值，也只显示删除按钮，不直接删除
        if (isSwipeActive) {
            // 保持删除按钮显示状态，让用户点击删除按钮
            chatElement.style.transform = 'translateX(-90px)';
            chatElement.style.transition = 'transform 0.3s ease';
        }

        chatElement.classList.remove('swipe-delete-ready-chat');
    }, { passive: true });

    // 触摸取消
    chatElement.addEventListener('touchcancel', (e) => {
        resetSwipe();
        chatElement.classList.remove('swipe-delete-ready-chat');
    }, { passive: true });

    // 点击其他地方时重置滑动状态
    document.addEventListener('touchstart', (e) => {
        if (isSwipeActive && !chatElement.contains(e.target)) {
            resetSwipe();
        }
    }, { passive: true });
}



// 显示移动端聊天操作提示
function showMobileChatTipsIfNeeded() {
    // 检测是否为移动设备或触摸设备
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
    const isSmallScreen = window.innerWidth <= 768;

    // 只在移动设备或小屏幕触摸设备上显示提示
    if ((isMobile || (isTouchDevice && isSmallScreen)) && chats.length > 0) {
        const mobileChatTips = document.getElementById('mobile-chat-tips');
        if (mobileChatTips) {
            // 检查是否已经显示过提示（使用localStorage记录）
            const hasShownChatTips = localStorage.getItem('chat-mobile-tips-shown');

            if (!hasShownChatTips) {
                // 延迟显示提示，让用户先看到聊天列表
                setTimeout(() => {
                    mobileChatTips.style.display = 'block';

                    // 3秒后自动隐藏提示
                    setTimeout(() => {
                        mobileChatTips.style.display = 'none';
                        // 记录已显示过提示
                        localStorage.setItem('chat-mobile-tips-shown', 'true');
                    }, 3000);
                }, 1000);
            }
        }
    }
}



// ==================== WebSocket 集成功能 ====================

// 初始化全局WebSocket事件监听（用于接收消息和更新徽标）
function initGlobalWebSocketListeners() {
    if (!window.wsManager) {
        console.warn('WebSocket管理器未初始化，将在1秒后重试');
        setTimeout(initGlobalWebSocketListeners, 1000);
        return;
    }

    console.log('初始化全局WebSocket监听器...');

    // 先移除可能存在的旧监听器，避免重复绑定
    window.wsManager.off('onNewMessage', handleWebSocketNewMessage);
    window.wsManager.off('onMessagesRead', handleWebSocketMessagesRead);
    window.wsManager.off('group_message', handleWebSocketGroupMessage);
    window.wsManager.off('group_messages_read', handleWebSocketGroupMessagesRead);

    // 监听新消息（全局，用于更新徽标和聊天列表）
    window.wsManager.on('onNewMessage', handleWebSocketNewMessage);

    // 监听消息已读状态（全局）
    window.wsManager.on('onMessagesRead', handleWebSocketMessagesRead);

    // 监听群聊消息
    window.wsManager.on('group_message', handleWebSocketGroupMessage);

    // 监听群聊消息已读状态
    window.wsManager.on('group_messages_read', handleWebSocketGroupMessagesRead);

    console.log('全局WebSocket监听器初始化完成');
    console.log('已注册的group_messages_read回调数量:', window.wsManager.callbacks.group_messages_read.length);
}

// 初始化聊天页面特定的WebSocket事件监听
function initChatPageWebSocketListeners() {
    if (!window.wsManager) {
        console.warn('WebSocket管理器未初始化');
        return;
    }

    // 先移除可能存在的旧监听器，避免重复绑定
    window.wsManager.off('onUserOnline', handleWebSocketUserOnline);
    window.wsManager.off('onUserOffline', handleWebSocketUserOffline);
    window.wsManager.off('onUserTyping', handleWebSocketUserTyping);
    window.wsManager.off('onUserOnlineStatus', handleWebSocketUserOnlineStatus);
    window.wsManager.off('onMultipleUsersStatus', handleWebSocketMultipleUsersStatus);

    // 监听用户在线状态（仅在聊天页面处理聊天相关的在线状态）
    window.wsManager.on('onUserOnline', handleWebSocketUserOnline);
    window.wsManager.on('onUserOffline', handleWebSocketUserOffline);

    // 监听用户输入状态
    window.wsManager.on('onUserTyping', handleWebSocketUserTyping);
}

// 兼容旧的函数名
function initWebSocketListeners() {
    initChatPageWebSocketListeners();
}

// 测试全局消息接收功能
function testGlobalMessageReceive() {
    const testMessages = [
        '这是一条测试消息',
        '测试徽标更新功能',
        '模拟从其他用户接收消息',
        '检查未读消息计数是否正确'
    ];

    const randomMessage = testMessages[Math.floor(Math.random() * testMessages.length)];
    const testSenderId = Math.floor(Math.random() * 100) + 1; // 随机发送者ID

    const mockMessageData = {
        id: Date.now(),
        sender_id: testSenderId,
        sender_name: `测试用户${testSenderId}`,
        sender_avatar: `https://picsum.photos/seed/user${testSenderId}/100/100`,
        content: randomMessage,
        time: formatMessageTime(new Date()),
        message_type: 'text'
    };

    console.log('模拟接收全局消息:', mockMessageData);
    handleWebSocketNewMessage(mockMessageData);
}

// 存储已处理的消息ID，防止重复处理
const processedMessageIds = new Set();

// 处理WebSocket新消息
function handleWebSocketNewMessage(messageData) {
    console.log('WebSocket收到新消息:', messageData);

    // 防止重复处理同一条消息
    if (messageData.id && processedMessageIds.has(messageData.id)) {
        console.log('消息已处理，跳过:', messageData.id);
        return;
    }

    // 记录已处理的消息ID
    if (messageData.id) {
        processedMessageIds.add(messageData.id);
        // 限制缓存大小，避免内存泄漏
        if (processedMessageIds.size > 1000) {
            const firstId = processedMessageIds.values().next().value;
            processedMessageIds.delete(firstId);
        }
    }

    // 如果聊天列表未初始化，先获取聊天列表
    if (!chats || chats.length === 0) {
        console.log('聊天列表未初始化，正在获取...');
        fetchChats().then(() => {
            // 获取完成后重新处理这条消息（但不递归调用，直接处理）
            console.log('聊天列表获取完成，继续处理消息');
        }).catch(error => {
            console.error('获取聊天列表失败:', error);
        });

        // 即使聊天列表为空，也要尝试更新徽标
        // 创建临时聊天项来处理这条消息
        const tempChat = {
            id: messageData.sender_id,
            name: messageData.sender_name || '未知用户',
            avatar: messageData.sender_avatar || `https://picsum.photos/seed/user${messageData.sender_id}/100/100`,
            lastMessage: messageData.content,
            time: messageData.time,
            unread: currentChatId !== messageData.sender_id ? 1 : 0
        };

        if (!chats) chats = [];
        chats.unshift(tempChat);

        updateChatTabBadge();
        return;
    }

    // 更新聊天列表中的最新消息
    let chat = chats.find(c => c.id === messageData.sender_id);

    // 如果聊天不存在，创建新的聊天项
    if (!chat) {
        chat = {
            id: messageData.sender_id,
            name: messageData.sender_name || '未知用户',
            avatar: messageData.sender_avatar || `https://picsum.photos/seed/user${messageData.sender_id}/100/100`,
            lastMessage: messageData.content,
            time: messageData.time,
            unread: 0
        };
        chats.unshift(chat);
    } else {
        chat.lastMessage = messageData.content;
        chat.time = messageData.time;

        // 将聊天移到列表顶部
        const chatIndex = chats.indexOf(chat);
        if (chatIndex > 0) {
            chats.splice(chatIndex, 1);
            chats.unshift(chat);
        }
    }

    // 如果不是当前聊天，增加未读数
    if (currentChatId !== messageData.sender_id) {
        chat.unread = (chat.unread || 0) + 1;
    }

    // 更新聊天列表显示和徽标
    renderChats();
    updateChatTabBadge();

    // 如果是当前聊天，直接添加消息到聊天界面
    if (currentChatId === messageData.sender_id) {
        const chatMessages = document.getElementById('chat-messages');
        if (chatMessages) {
            // 检查是否已存在相同的消息（基于时间和内容）
            const existingMessages = chatMessages.querySelectorAll('.message');
            const isDuplicate = Array.from(existingMessages).some(msg => {
                const timeElement = msg.querySelector('.message-time');
                const contentElement = msg.querySelector('.message-content');
                return timeElement && contentElement &&
                       timeElement.textContent === messageData.time &&
                       contentElement.textContent === messageData.content;
            });

            if (!isDuplicate) {
                const messageElement = createMessageElement(messageData, chat);
                chatMessages.appendChild(messageElement);

                // 滚动到底部
                chatMessages.scrollTop = chatMessages.scrollHeight;

                // 标记消息为已读
                if (messageData.id) {
                    setTimeout(() => {
                        window.wsManager.markMessagesAsRead([messageData.id], messageData.sender_id);
                    }, 500);
                }
            }
        }
    } else {
        // 移除接收消息的弹出提示，因为显示未读了好友看到自然明白了
        // showToast(`${messageData.sender_name || '新消息'}: ${messageData.content}`, 'info');
    }
}

// 处理WebSocket群聊消息
function handleWebSocketGroupMessage(messageData) {
    console.log('WebSocket收到群聊消息:', messageData);

    // 防止重复处理同一条消息
    if (messageData.id && processedMessageIds.has(messageData.id)) {
        console.log('群聊消息已处理，跳过:', messageData.id);
        return;
    }

    // 记录已处理的消息ID
    if (messageData.id) {
        processedMessageIds.add(messageData.id);
        if (processedMessageIds.size > 1000) {
            const firstId = processedMessageIds.values().next().value;
            processedMessageIds.delete(firstId);
        }
    }

    // 查找对应的群聊
    let groupChat = chats.find(c => c.id === messageData.group_id && c.type === 'group');

    if (!groupChat && chats.length > 0) {
        // 如果找不到群聊，可能是新创建的群，重新获取聊天列表
        fetchChats().then(() => {
            console.log('重新获取聊天列表后处理群聊消息');
        });
        return;
    }

    // 更新聊天列表中的最新消息
    if (groupChat) {
        groupChat.lastMessage = messageData.content;
        groupChat.time = messageData.time;
        
        // 如果不是当前打开的群聊，增加未读数
        if (currentChatId !== messageData.group_id) {
            groupChat.unread = (groupChat.unread || 0) + 1;
        }

        // 将群聊移到列表顶部
        const index = chats.indexOf(groupChat);
        if (index > 0) {
            chats.splice(index, 1);
            chats.unshift(groupChat);
        }

        renderChats();
        updateChatTabBadge();
    }

    // 如果是当前打开的群聊，实时显示消息
    if (currentChatId === messageData.group_id) {
        const chatMessages = document.getElementById('chat-messages');
        if (chatMessages) {
            // 确定消息类型（自己发送的消息显示为sent，其他人的显示为received）
            let currentUserInfo = null;
            try {
                currentUserInfo = JSON.parse(localStorage.getItem('wechat_user'));
            } catch (e) {
                console.error('获取当前用户信息失败', e);
            }

            // 如果是自己发送的消息，不要重复添加（因为sendMessage已经添加了临时消息）
            const isOwnMessage = currentUserInfo && currentUserInfo.id === messageData.sender_id;
            if (isOwnMessage) {
                // 查找并更新对应的临时消息，将其标记为已发送
                const tempMessageElements = chatMessages.querySelectorAll('[data-temp-id]');
                let tempMessageFound = false;

                tempMessageElements.forEach(tempElement => {
                    let isMatch = false;

                    // 获取临时消息的类型和文件名
                    const tempMessageType = tempElement.dataset.messageType;
                    const tempFileName = tempElement.dataset.fileName;

                    // 根据消息类型进行不同的匹配策略
                    if (messageData.message_type === 'text') {
                        // 文本消息：通过内容匹配
                        const messageContent = tempElement.querySelector('.message-content');
                        if (messageContent) {
                            const textContent = messageContent.textContent || messageContent.innerText || '';
                            isMatch = textContent.trim() === messageData.content.trim();
                        }
                    } else if (['image', 'video', 'file'].includes(messageData.message_type)) {
                        // 文件消息：通过文件名和消息类型匹配
                        isMatch = tempMessageType === messageData.message_type &&
                                 tempFileName === messageData.file_name;
                    }

                    if (isMatch) {
                        // 找到匹配的临时消息，更新其状态
                        const statusElement = tempElement.querySelector('.message-status');
                        if (statusElement) {
                            statusElement.className = 'message-status sent';
                            statusElement.innerHTML = '<i class="fas fa-check"></i>';
                            statusElement.title = '已发送';
                        }
                        // 更新消息ID
                        tempElement.dataset.messageId = messageData.id;
                        tempElement.removeAttribute('data-temp-id');
                        tempElement.removeAttribute('data-file-name');
                        tempElement.removeAttribute('data-message-type');

                        // 移除上传中的样式
                        const uploadingElements = tempElement.querySelectorAll('.uploading');
                        uploadingElements.forEach(el => el.classList.remove('uploading'));

                        // 更新时间显示
                        const timeElement = tempElement.querySelector('.message-time');
                        if (timeElement) {
                            timeElement.textContent = messageData.time;
                        }
                        tempMessageFound = true;
                    }
                });
                
                // 如果没有找到对应的临时消息，说明可能是从其他页面发送的，此时需要添加
                if (!tempMessageFound) {
                    const message = {
                        id: messageData.id,
                        type: 'sent',
                        content: messageData.content,
                        message_type: messageData.message_type || 'text',
                        file_url: messageData.file_url,
                        file_name: messageData.file_name,
                        file_size: messageData.file_size,
                        time: messageData.time,
                        sender_name: messageData.sender_name,
                        sender_avatar: messageData.sender_avatar
                    };

                    const messageElement = createMessageElement(message, groupChat);
                    chatMessages.appendChild(messageElement);
                    chatMessages.scrollTop = chatMessages.scrollHeight;
                }
            } else {
                // 其他人的消息，正常添加
                if (chatMessages.querySelector('.empty-messages')) {
                    chatMessages.innerHTML = '';
                }

                const message = {
                    id: messageData.id,
                    type: 'received',
                    content: messageData.content,
                    message_type: messageData.message_type || 'text',
                    file_url: messageData.file_url,
                    file_name: messageData.file_name,
                    file_size: messageData.file_size,
                    time: messageData.time,
                    sender_name: messageData.sender_name,
                    sender_avatar: messageData.sender_avatar
                };

                const messageElement = createMessageElement(message, groupChat);
                chatMessages.appendChild(messageElement);
                chatMessages.scrollTop = chatMessages.scrollHeight;
            }
        }
    } else {
        // 显示新消息通知（可选）
        // showToast(`${messageData.sender_name}: ${messageData.content}`, 'info');
    }
}

// 处理WebSocket消息已读状态更新
function handleWebSocketMessagesRead(data) {
    console.log('WebSocket收到消息已读通知:', data);

    // 更新界面中对应消息的已读状态
    data.message_ids.forEach(messageId => {
        const messageElement = document.querySelector(`[data-message-id="${messageId}"]`);
        if (messageElement) {
            const statusElement = messageElement.querySelector('.message-status');
            if (statusElement && statusElement.classList.contains('sent')) {
                statusElement.className = 'message-status read';
                statusElement.innerHTML = '<i class="fas fa-check"></i><i class="fas fa-check"></i>';
                statusElement.title = '已读';
            }
        }
    });
}

// 处理WebSocket群聊消息已读状态更新
function handleWebSocketGroupMessagesRead(data) {
    console.log('WebSocket收到群聊消息已读通知:', data);

    // 检查当前是否在聊天界面，如果在聊天界面且是对应的群聊，则实时更新
    const currentChatElement = document.querySelector('.chat-detail');
    const isInChatInterface = currentChatElement && currentChatElement.classList.contains('active');
    const isCurrentGroup = currentChatId && currentChatId == data.group_id;

    if (isInChatInterface && isCurrentGroup) {
        console.log('当前正在查看该群聊，开始实时更新已读状态，消息IDs:', data.message_ids);

        // 更新界面中对应消息的已读状态（只更新当前用户发送的消息）
        data.message_ids.forEach(messageId => {
            const messageElement = document.querySelector(`[data-message-id="${messageId}"]`);
            if (messageElement) {
                // 只更新当前用户发送的消息（sent类型的消息）
                if (messageElement.classList.contains('sent')) {
                    const statusElement = messageElement.querySelector('.message-status.group-read');
                    if (statusElement) {
                        console.log(`实时更新消息 ${messageId} 的已读状态`);
                        // 重新获取已读状态并更新显示
                        updateGroupMessageReadStatus(messageId, statusElement);
                    } else {
                        console.log(`消息 ${messageId} 没有找到已读状态元素`);
                    }
                } else {
                    console.log(`消息 ${messageId} 不是当前用户发送的，跳过更新`);
                }
            } else {
                console.log(`没有找到消息元素: ${messageId}`);
            }
        });

        // 同时更新所有当前用户发送的消息的已读状态（防止遗漏）
        const allSentMessageElements = document.querySelectorAll('.message.sent[data-message-id]');
        allSentMessageElements.forEach(messageElement => {
            const statusElement = messageElement.querySelector('.message-status.group-read');
            if (statusElement) {
                const messageId = messageElement.getAttribute('data-message-id');
                if (messageId) {
                    updateGroupMessageReadStatus(messageId, statusElement);
                }
            }
        });
    } else {
        console.log('不在对应群聊界面，已读状态将在下次打开群聊时更新，当前聊天ID:', currentChatId, '群聊ID:', data.group_id);
        // 不在当前群聊界面时，不需要立即更新UI
        // 已读状态会在用户下次打开该群聊时通过API获取最新状态
    }
}

// 更新群聊消息已读状态显示
function updateGroupMessageReadStatus(messageId, statusElement) {
    console.log(`开始更新消息 ${messageId} 的已读状态，状态元素:`, statusElement);

    fetch(`/api/group_messages/${messageId}/read_status`, {
        credentials: 'include'
    })
    .then(response => response.json())
    .then(data => {
        console.log(`消息 ${messageId} 的已读状态API响应:`, data);

        if (data.code === 0) {
            const readData = data.data;
            const readCount = readData.read_count || 0;
            const totalMembers = readData.total_members || 0;

            console.log(`消息 ${messageId} 已读人数: ${readCount}/${totalMembers}`);

            if (readCount > 0) {
                const newContent = `<i class="fas fa-check"></i> ${readCount}人已读`;
                console.log(`准备更新消息 ${messageId} 状态为: ${newContent}`);

                // 使用requestAnimationFrame确保在下一个渲染帧更新
                requestAnimationFrame(() => {
                    try {
                        console.log(`开始重新创建消息 ${messageId} 的状态元素`);

                        // 先移除元素
                        const parent = statusElement.parentElement;
                        console.log(`消息 ${messageId} 的父元素:`, parent);
                        statusElement.remove();
                        console.log(`消息 ${messageId} 的旧状态元素已移除`);

                        // 重新创建状态元素
                        const newStatusElement = document.createElement('span');
                        newStatusElement.className = 'message-status group-read';
                        newStatusElement.innerHTML = newContent;
                        newStatusElement.title = `${readCount}/${totalMembers} 人已读，点击查看详情`;
                        newStatusElement.style.cursor = 'pointer';
                        newStatusElement.style.display = 'inline-flex';
                        newStatusElement.style.visibility = 'visible';
                        newStatusElement.style.opacity = '1';

                        // 添加点击事件
                        newStatusElement.addEventListener('click', () => {
                            showGroupMessageReadDetails(messageId);
                        });

                        // 重新添加到父元素
                        parent.appendChild(newStatusElement);
                        console.log(`消息 ${messageId} 的新状态元素已添加`);

                        console.log(`消息 ${messageId} 状态已重新创建为: ${newContent}`);
                        console.log(`新状态元素:`, newStatusElement);
                        console.log(`父元素:`, parent);
                    } catch (error) {
                        console.error(`重新创建消息 ${messageId} 状态元素时出错:`, error);
                    }
                });
            } else {
                console.log(`准备更新消息 ${messageId} 状态为: 已发送`);

                // 使用requestAnimationFrame确保在下一个渲染帧更新
                requestAnimationFrame(() => {
                    try {
                        console.log(`开始重新创建消息 ${messageId} 的状态元素（已发送）`);

                        // 先移除元素
                        const parent = statusElement.parentElement;
                        console.log(`消息 ${messageId} 的父元素:`, parent);
                        statusElement.remove();
                        console.log(`消息 ${messageId} 的旧状态元素已移除`);

                        // 重新创建状态元素
                        const newStatusElement = document.createElement('span');
                        newStatusElement.className = 'message-status group-read';
                        newStatusElement.innerHTML = '<i class="fas fa-check"></i>';
                        newStatusElement.title = '已发送';
                        newStatusElement.style.display = 'inline-flex';
                        newStatusElement.style.visibility = 'visible';
                        newStatusElement.style.opacity = '1';

                        // 重新添加到父元素
                        parent.appendChild(newStatusElement);
                        console.log(`消息 ${messageId} 的新状态元素已添加`);

                        console.log(`消息 ${messageId} 状态已重新创建为: 已发送`);
                        console.log(`新状态元素:`, newStatusElement);
                        console.log(`父元素:`, parent);
                    } catch (error) {
                        console.error(`重新创建消息 ${messageId} 状态元素时出错:`, error);
                    }
                });
            }
        } else {
            console.error(`获取消息 ${messageId} 已读状态失败:`, data.msg);
        }
    })
    .catch(error => {
        console.error('更新群聊消息已读状态错误:', error);
    });
}

// 处理WebSocket用户上线通知（聊天页面专用）
function handleWebSocketUserOnline(data) {
    // 聊天页面可以在这里处理聊天相关的在线状态逻辑
    // 例如更新聊天列表中的在线状态指示器等
    // 通讯录的在线状态由contacts.js单独处理
}

// 处理WebSocket用户下线通知（聊天页面专用）
function handleWebSocketUserOffline(data) {
    // 聊天页面可以在这里处理聊天相关的在线状态逻辑
    // 通讯录的在线状态由contacts.js单独处理
}

// 处理WebSocket用户输入状态
function handleWebSocketUserTyping(data) {
    console.log('WebSocket收到用户输入状态:', data);

    // 如果是当前聊天对象，显示输入状态
    if (currentChatId === data.user_id) {
        showTypingIndicator(data.typing);
    }
}

// 显示/隐藏输入状态指示器
function showTypingIndicator(isTyping) {
    const chatMessages = document.getElementById('chat-messages');
    if (!chatMessages) return;

    let typingIndicator = document.getElementById('typing-indicator');

    if (isTyping) {
        if (!typingIndicator) {
            typingIndicator = document.createElement('div');
            typingIndicator.id = 'typing-indicator';
            typingIndicator.className = 'typing-indicator';
            typingIndicator.innerHTML = `
                <div class="typing-dots">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
                <span class="typing-text">对方正在输入...</span>
            `;
            chatMessages.appendChild(typingIndicator);

            // 滚动到底部
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }
    } else {
        if (typingIndicator) {
            typingIndicator.remove();
        }
    }
}

// 处理输入状态
function handleTypingStatus(event, isTyping = true) {
    if (!currentChatId || !window.wsManager) return;

    if (event && event.target.value.trim() === '') {
        isTyping = false;
    }

    window.wsManager.handleTyping(currentChatId, isTyping);
}

// 重写showChatDetail函数，添加WebSocket房间管理
const originalShowChatDetail = window.showChatDetail;
window.showChatDetail = function(chatId, chatObject = null) {
    // 确保全局WebSocket监听器已初始化
    if (window.wsManager && !window.wsManager._chatListenersInitialized) {
        console.log('在showChatDetail中初始化全局WebSocket监听器');
        initGlobalWebSocketListeners();
        window.wsManager._chatListenersInitialized = true;
    }

    // 离开之前的聊天房间
    if (currentChatId && window.wsManager) {
        window.wsManager.leaveChat(currentChatId);
    }

    // 调用原始函数
    if (originalShowChatDetail) {
        originalShowChatDetail(chatId, chatObject);
    }

    // 加入新的聊天房间
    if (chatId && window.wsManager) {
        window.wsManager.joinChat(chatId);
    }
}

// 处理WebSocket用户在线状态更新
function handleWebSocketUserOnlineStatus(data) {
    console.log('WebSocket收到用户在线状态更新:', data);

    // 如果有联系人页面的更新函数，调用它
    if (window.updateContactOnlineStatus) {
        window.updateContactOnlineStatus(data.user_id, data.is_online);
    }
}

// 处理WebSocket多个用户在线状态更新
function handleWebSocketMultipleUsersStatus(data) {
    console.log('WebSocket收到多个用户在线状态更新:', data);

    // 如果有联系人页面的更新函数，调用它
    if (window.updateMultipleContactsStatus) {
        window.updateMultipleContactsStatus(data);
    }
};

// 导出全局变量和函数
window.chats = chats;
window.renderChats = renderChats;
window.showChatDetail = showChatDetail;
// 初始化聊天下拉刷新功能
function initChatPullToRefresh() {
    const chatPage = document.getElementById('chat-page');
    if (!chatPage || !window.PullToRefresh) {
        return;
    }

    // 销毁之前的实例
    if (chatPullToRefresh) {
        chatPullToRefresh.destroy();
    }

    // 创建新的下拉刷新实例
    chatPullToRefresh = new PullToRefresh(chatPage, {
        threshold: 80,
        maxDistance: 120,
        resistance: 2.5,
        refreshText: '下拉刷新聊天',
        releaseText: '松开刷新聊天',
        loadingText: '正在刷新聊天...',
        completeText: '聊天刷新完成',
        onRefresh: () => {
            return new Promise((resolve) => {
                // 重新获取聊天数据
                fetch('/api/chats', {
                    credentials: 'include'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.code === 0) {
                        chats = data.data;
                        renderChats();
                        console.log('聊天刷新成功');
                    } else {
                        console.error('刷新聊天失败:', data.msg);
                    }
                    resolve();
                })
                .catch(error => {
                    console.error('刷新聊天错误:', error);
                    resolve();
                });
            });
        }
    });
}

window.startNewChat = startNewChat;
window.initChat = initChat; // 导出初始化函数
window.simulateNewMessage = simulateNewMessage; // 用于测试
window.testGlobalMessageReceive = testGlobalMessageReceive; // 用于测试全局消息接收
window.initWebSocketListeners = initWebSocketListeners;
window.initGlobalWebSocketListeners = initGlobalWebSocketListeners;
window.initChatPageWebSocketListeners = initChatPageWebSocketListeners;


// ==================== 群聊功能 ====================

// 群聊相关变量
let selectedMembers = [];
let groupChats = [];
let currentGroupId = null;

// 初始化群聊功能
function initGroupChat() {
    // 创建群聊按钮
    const groupChatBtn = document.getElementById('group-chat-btn');
    if (groupChatBtn) {
        groupChatBtn.addEventListener('click', showCreateGroupChat);
    }

    // 返回按钮
    const backToChatList = document.getElementById('back-to-chat-list');
    if (backToChatList) {
        backToChatList.addEventListener('click', hideCreateGroupChat);
    }

    // 群聊详情返回按钮
    const backToGroupChat = document.getElementById('back-to-group-chat');
    if (backToGroupChat) {
        backToGroupChat.addEventListener('click', hideGroupChatDetail);
    }

    // 创建群聊确认按钮
    const createGroupConfirm = document.getElementById('create-group-confirm');
    if (createGroupConfirm) {
        createGroupConfirm.addEventListener('click', createGroup);
    }

    // 群聊名称输入框
    const groupNameInput = document.getElementById('group-name-input');
    if (groupNameInput) {
        groupNameInput.addEventListener('input', updateGroupNameCounter);
        groupNameInput.addEventListener('input', validateGroupCreation);
    }

    // 成员搜索
    const memberSearch = document.getElementById('member-search');
    if (memberSearch) {
        memberSearch.addEventListener('input', filterFriends);
    }

    // 群头像上传
    const groupAvatarUpload = document.getElementById('group-avatar-upload');
    if (groupAvatarUpload) {
        groupAvatarUpload.addEventListener('change', handleGroupAvatarUpload);
    }

    // 更换头像按钮
    const changeAvatarBtn = document.getElementById('change-avatar-btn');
    if (changeAvatarBtn) {
        changeAvatarBtn.addEventListener('click', () => {
            document.getElementById('group-avatar-upload').click();
        });
    }

    // 群聊详情相关事件
    const chatMoreBtn = document.getElementById('chat-more-btn');
    if (chatMoreBtn) {
        chatMoreBtn.addEventListener('click', showGroupChatDetail);
    }

    // 群聊操作事件
    const leaveGroup = document.getElementById('leave-group');
    if (leaveGroup) {
        leaveGroup.addEventListener('click', handleLeaveGroup);
    }

    const dissolveGroup = document.getElementById('dissolve-group');
    if (dissolveGroup) {
        dissolveGroup.addEventListener('click', handleDissolveGroup);
    }

    // 添加成员按钮
    const addMemberBtn = document.getElementById('add-member-btn');
    if (addMemberBtn) {
        addMemberBtn.addEventListener('click', showAddMember);
    }

    // 获取已有群聊
    fetchGroupChats();
}

// 显示创建群聊页面
function showCreateGroupChat() {
    console.log('显示创建群聊页面');
    
    const createGroupChat = document.getElementById('create-group-chat');
    if (createGroupChat) {
        createGroupChat.classList.add('active');
        console.log('创建群聊页面已显示');
        
        // 重置选择状态
        selectedMembers = [];
        updateSelectedMembers();
        
        // 延迟加载好友列表，确保页面已完全显示
        setTimeout(() => {
            console.log('开始加载好友列表...');
            loadFriendsForGroupCreation();
        }, 300);
    } else {
        console.error('找不到创建群聊页面元素 #create-group-chat');
    }
}

// 隐藏创建群聊页面
function hideCreateGroupChat() {
    const createGroupChat = document.getElementById('create-group-chat');
    if (createGroupChat) {
        createGroupChat.classList.remove('active');
        selectedMembers = [];
        // 重置表单
        const groupNameInput = document.getElementById('group-name-input');
        if (groupNameInput) {
            groupNameInput.value = '';
        }
        updateGroupNameCounter();
        validateGroupCreation();
    }
}

// 显示群聊详情页面
function showGroupChatDetail() {
    if (currentChatId && isGroupChat(currentChatId)) {
        const groupChatDetail = document.getElementById('group-chat-detail');
        if (groupChatDetail) {
            groupChatDetail.classList.add('active');
            loadGroupDetails(currentChatId);
        }
    }
}

// 隐藏群聊详情页面
function hideGroupChatDetail() {
    const groupChatDetail = document.getElementById('group-chat-detail');
    if (groupChatDetail) {
        groupChatDetail.classList.remove('active');
    }
}

// 判断是否为群聊
function isGroupChat(chatId) {
    const chat = chats.find(c => c.id === chatId);
    return chat && chat.type === 'group';
}

// 加载好友列表用于群聊创建
async function loadFriendsForGroupCreation() {
    console.log('开始加载好友列表用于群聊创建...');
    
    try {
        const response = await fetch('/api/contacts', {
            credentials: 'include'
        });
        
        console.log('API响应状态:', response.status);
        const data = await response.json();
        console.log('API返回数据:', data);
        
        if (data.code === 0) {
            // 检查数据结构，后端返回的是 {current_user: ..., contacts: [...]}
            let friends = [];
            if (data.data && data.data.contacts) {
                friends = data.data.contacts;
            } else if (data.data && Array.isArray(data.data)) {
                friends = data.data;
            } else {
                console.warn('意外的数据结构:', data.data);
                friends = [];
            }
            
            console.log('好友列表获取成功，好友数量:', friends.length);
            renderFriendsForGroupCreation(friends);
        } else {
            console.error('API返回错误:', data.msg);
            showToast(data.msg || '获取好友列表失败', 'error');
            
            // 如果API失败，使用模拟数据进行测试
            console.log('使用模拟好友数据...');
            const mockFriends = [
                { id: 1, username: '张三', avatar: '', online: true },
                { id: 2, username: '李四', avatar: '', online: false },
                { id: 3, username: '王五', avatar: '', online: true },
                { id: 4, username: '赵六', avatar: '', online: false },
                { id: 5, username: '田七', avatar: '', online: true }
            ];
            renderFriendsForGroupCreation(mockFriends);
        }
    } catch (error) {
        console.error('加载好友列表错误:', error);
        showToast('网络连接异常，使用模拟数据', 'warning');
        
        // 使用模拟数据
        const mockFriends = [
            { id: 1, username: '张三', avatar: '', online: true },
            { id: 2, username: '李四', avatar: '', online: false },
            { id: 3, username: '王五', avatar: '', online: true },
            { id: 4, username: '赵六', avatar: '', online: false },
            { id: 5, username: '田七', avatar: '', online: true }
        ];
        renderFriendsForGroupCreation(mockFriends);
    }
}

// 渲染好友列表用于群聊创建
function renderFriendsForGroupCreation(friends) {
    console.log('开始渲染好友列表，好友数据:', friends);
    
    const friendsList = document.getElementById('create-group-friends-list');
    if (!friendsList) {
        console.error('找不到好友列表容器元素 #create-group-friends-list');
        return;
    }

    console.log('找到好友列表容器，开始清空并渲染');
    friendsList.innerHTML = '';

    if (!friends || friends.length === 0) {
        friendsList.innerHTML = '<div class="no-friends-message">暂无好友可选择</div>';
        console.log('好友列表为空');
        return;
    }

    friends.forEach((friend, index) => {
        console.log(`渲染第${index + 1}个好友:`, friend);
        
        const friendItem = document.createElement('div');
        friendItem.className = 'friend-item';
        friendItem.setAttribute('data-friend-id', friend.id);
        
        const avatarUrl = friend.avatar || `https://picsum.photos/seed/user${friend.id}/100/100`;
        // 兼容不同的字段名：username（后端）或 name（模拟数据）
        const friendName = friend.username || friend.name || '未知用户';
        // 获取在线状态
        const isOnline = friend.online !== undefined ? friend.online : (friend.is_online || false);
        const onlineStatus = isOnline ? '在线' : '离线';
        
        friendItem.innerHTML = `
            <div class="friend-avatar" style="background-image: url('${avatarUrl}');">
                ${!friend.avatar ? friendName.charAt(0).toUpperCase() : ''}
            </div>
            <div class="friend-info">
                <div class="friend-name">${friendName}</div>
                <div class="friend-status">${onlineStatus}</div>
            </div>
            <div class="selection-indicator">
                <i class="fas fa-check"></i>
            </div>
        `;

        friendItem.addEventListener('click', () => {
            console.log('点击了好友:', friend);
            // 确保传递给toggleMemberSelection的friend对象包含name字段
            const normalizedFriend = {
                id: friend.id,
                name: friendName,
                username: friendName,
                avatar: friend.avatar,
                online: isOnline
            };
            toggleMemberSelection(normalizedFriend, friendItem);
        });
        
        friendsList.appendChild(friendItem);
    });
    
    console.log(`好友列表渲染完成，共${friends.length}个好友`);
}

// 切换成员选择状态
function toggleMemberSelection(friend, friendItem) {
    console.log('切换成员选择状态:', friend, friendItem);
    
    const index = selectedMembers.findIndex(m => m.id === friend.id);
    
    if (index > -1) {
        // 取消选择
        console.log('取消选择好友:', friend.name);
        selectedMembers.splice(index, 1);
        friendItem.classList.remove('selected');
    } else {
        // 选择
        console.log('选择好友:', friend.name);
        selectedMembers.push(friend);
        friendItem.classList.add('selected');
    }

    console.log('当前已选择成员:', selectedMembers);
    updateSelectedMembers();
    validateGroupCreation();
}

// 更新已选择的成员显示
function updateSelectedMembers() {
    console.log('更新已选择的成员显示:', selectedMembers);
    
    const selectedMembersContainer = document.getElementById('selected-members');
    const selectedCount = document.getElementById('selected-count');
    
    if (!selectedMembersContainer || !selectedCount) {
        console.error('找不到选择成员容器元素');
        return;
    }

    selectedCount.textContent = `已选择 ${selectedMembers.length} 人`;
    
    selectedMembersContainer.innerHTML = '';
    
    selectedMembers.forEach(member => {
        const memberTag = document.createElement('div');
        memberTag.className = 'selected-member';
        memberTag.innerHTML = `
            <span>${member.name}</span>
            <div class="remove-btn" data-member-id="${member.id}">
                <i class="fas fa-times"></i>
            </div>
        `;

        const removeBtn = memberTag.querySelector('.remove-btn');
        removeBtn.addEventListener('click', () => {
            console.log('移除选择的成员:', member.name);
            removeMemberFromSelection(member.id);
        });

        selectedMembersContainer.appendChild(memberTag);
    });
    
    console.log('已选择成员显示更新完成');
}

// 从选择中移除成员
function removeMemberFromSelection(memberId) {
    console.log('从选择中移除成员ID:', memberId);
    
    const index = selectedMembers.findIndex(m => m.id === memberId);
    if (index > -1) {
        const removedMember = selectedMembers[index];
        console.log('移除成员:', removedMember.name);
        
        selectedMembers.splice(index, 1);
        updateSelectedMembers();
        validateGroupCreation();

        // 更新好友列表中的选择状态
        const friendItem = document.querySelector(`[data-friend-id="${memberId}"]`);
        if (friendItem) {
            console.log('更新好友列表中的选择状态');
            friendItem.classList.remove('selected');
        } else {
            console.log('未找到对应的好友列表项');
        }
    } else {
        console.log('未找到要移除的成员');
    }
}

// 验证群聊创建条件
function validateGroupCreation() {
    const createGroupConfirm = document.getElementById('create-group-confirm');
    const groupNameInput = document.getElementById('group-name-input');
    
    if (!createGroupConfirm || !groupNameInput) return;

    const hasName = groupNameInput.value.trim().length > 0;
    const hasMembers = selectedMembers.length >= 2; // 至少需要2个成员

    createGroupConfirm.disabled = !(hasName && hasMembers);
}

// 更新群聊名称字符计数
function updateGroupNameCounter() {
    const groupNameInput = document.getElementById('group-name-input');
    const inputCounter = document.querySelector('.input-counter');
    
    if (!groupNameInput || !inputCounter) return;

    const length = groupNameInput.value.length;
    inputCounter.textContent = `${length}/20`;
}

// 过滤好友列表
function filterFriends() {
    const memberSearch = document.getElementById('member-search');
    const friendItems = document.querySelectorAll('.friend-item');
    
    if (!memberSearch) return;

    const searchTerm = memberSearch.value.toLowerCase();

    friendItems.forEach(item => {
        const nameElement = item.querySelector('.friend-name');
        if (nameElement) {
            const name = nameElement.textContent.toLowerCase();
            if (name.includes(searchTerm)) {
                item.style.display = 'flex';
            } else {
                item.style.display = 'none';
            }
        }
    });
}

// 处理群头像上传
function handleGroupAvatarUpload(event) {
    const file = event.target.files[0];
    if (!file) return;

    // 验证文件类型
    if (!file.type.startsWith('image/')) {
        showToast('请选择图片文件', 'error');
        return;
    }

    // 验证文件大小
    if (file.size > 5 * 1024 * 1024) {
        showToast('图片文件不能超过5MB', 'error');
        return;
    }

    // 创建文件读取器
    const reader = new FileReader();
    reader.onload = function(e) {
        const groupAvatarPreview = document.getElementById('group-avatar-preview');
        if (groupAvatarPreview) {
            groupAvatarPreview.style.backgroundImage = `url('${e.target.result}')`;
            groupAvatarPreview.innerHTML = ''; // 清空默认图标
        }
    };
    reader.readAsDataURL(file);
}

// 创建群聊
async function createGroup() {
    const groupNameInput = document.getElementById('group-name-input');
    const groupAvatarUpload = document.getElementById('group-avatar-upload');
    
    if (!groupNameInput) return;

    const groupName = groupNameInput.value.trim();
    if (!groupName) {
        showToast('请输入群聊名称', 'error');
        return;
    }

    if (selectedMembers.length < 2) {
        showToast('至少需要选择2个成员', 'error');
        return;
    }

    const createGroupConfirm = document.getElementById('create-group-confirm');
    if (createGroupConfirm) {
        createGroupConfirm.disabled = true;
        createGroupConfirm.textContent = '创建中...';
    }

    try {
        const formData = new FormData();
        formData.append('name', groupName);
        formData.append('member_ids', JSON.stringify(selectedMembers.map(m => m.id)));
        
        if (groupAvatarUpload && groupAvatarUpload.files[0]) {
            formData.append('avatar', groupAvatarUpload.files[0]);
        }

        const response = await fetch('/api/groups', {
            method: 'POST',
            credentials: 'include',
            body: formData
        });

        const data = await response.json();

        if (data.code === 0) {
            showToast('群聊创建成功', 'success');
            
            // 添加到聊天列表
            const newGroupChat = {
                id: data.data.id,
                name: groupName,
                avatar: data.data.avatar,
                type: 'group',
                lastMessage: '群聊已创建',
                time: formatMessageTime(new Date()),
                unread: 0,
                members: selectedMembers.length + 1 // 包含创建者
            };

            chats.unshift(newGroupChat);
            renderChats();
            
            // 隐藏创建页面
            hideCreateGroupChat();
            
            // 打开新创建的群聊
            setTimeout(() => {
                showChatDetail(newGroupChat.id, newGroupChat);
            }, 200);
        } else {
            showToast(data.msg || '创建群聊失败', 'error');
        }
    } catch (error) {
        console.error('创建群聊错误:', error);
        showToast('网络连接异常，请重试', 'error');
    } finally {
        if (createGroupConfirm) {
            createGroupConfirm.disabled = false;
            createGroupConfirm.textContent = '完成';
        }
    }
}

// 获取群聊列表
async function fetchGroupChats() {
    try {
        const response = await fetch('/api/groups', {
            credentials: 'include'
        });
        const data = await response.json();
        
        if (data.code === 0) {
            groupChats = data.data;
            // 合并到聊天列表中
            mergeGroupChatsToChats();
        }
    } catch (error) {
        console.error('获取群聊列表错误:', error);
    }
}

// 将群聊合并到聊天列表
function mergeGroupChatsToChats() {
    groupChats.forEach(group => {
        const existingChat = chats.find(c => c.id === group.id && c.type === 'group');
        if (!existingChat) {
            chats.push({
                id: group.id,
                name: group.name,
                avatar: group.avatar,
                type: 'group',
                lastMessage: group.last_message || '暂无消息',
                time: group.last_message_time || formatMessageTime(new Date()),
                unread: group.unread || 0,
                members: group.member_count
            });
        }
    });
    renderChats();
}

// 加载群聊详情
async function loadGroupDetails(groupId) {
    try {
        const response = await fetch(`/api/groups/${groupId}`, {
            credentials: 'include'
        });
        const data = await response.json();
        
        if (data.code === 0) {
            renderGroupDetails(data.data);
        } else {
            showToast(data.msg || '获取群聊详情失败', 'error');
        }
    } catch (error) {
        console.error('加载群聊详情错误:', error);
        showToast('网络连接异常，请重试', 'error');
    }
}

// 渲染群聊详情
function renderGroupDetails(group) {
    // 更新群聊基本信息
    const groupNameDisplay = document.getElementById('group-name-display');
    const groupMemberCount = document.getElementById('group-member-count');
    const groupAvatarLarge = document.getElementById('group-avatar-large');
    
    if (groupNameDisplay) groupNameDisplay.textContent = group.name;
    if (groupMemberCount) groupMemberCount.textContent = `${group.members.length}人`;
    
    if (groupAvatarLarge) {
        if (group.avatar) {
            groupAvatarLarge.style.backgroundImage = `url('${group.avatar}')`;
            groupAvatarLarge.innerHTML = '';
        } else {
            groupAvatarLarge.innerHTML = '<i class="fas fa-users"></i>';
        }
    }

    // 渲染群成员
    renderGroupMembers(group.members, group.owner_id);

    // 显示/隐藏群主功能
    const currentUser = JSON.parse(localStorage.getItem('wechat_user') || '{}');
    const dissolveGroup = document.getElementById('dissolve-group');
    if (dissolveGroup) {
        if (group.owner_id === currentUser.id) {
            dissolveGroup.style.display = 'flex';
        } else {
            dissolveGroup.style.display = 'none';
        }
    }
}

// 渲染群成员
function renderGroupMembers(members, ownerId) {
    const groupMembersGrid = document.getElementById('group-members-grid');
    if (!groupMembersGrid) return;

    groupMembersGrid.innerHTML = '';

    members.forEach(member => {
        const memberItem = document.createElement('div');
        memberItem.className = 'member-item';
        
        const isOwner = member.id === ownerId;
        
        memberItem.innerHTML = `
            <div class="member-avatar" style="background-image: url('${member.avatar || `https://picsum.photos/seed/user${member.id}/100/100`}');">
                ${!member.avatar ? (member.name ? member.name.charAt(0).toUpperCase() : 'U') : ''}
            </div>
            <div class="member-name">${member.name}</div>
            ${isOwner ? '<div class="owner-badge">群主</div>' : ''}
        `;

        memberItem.addEventListener('click', () => showMemberProfile(member));
        groupMembersGrid.appendChild(memberItem);
    });
}

// 显示成员资料
function showMemberProfile(member) {
    // 这里可以实现成员资料弹窗
    console.log('显示成员资料:', member);
}

// 处理退出群聊
async function handleLeaveGroup() {
    if (!currentChatId) return;

    const result = await showConfirmDialog(
        '确认退出群聊？',
        '退出后将无法接收群消息，确定要退出吗？'
    );

    if (!result) return;

    try {
        const response = await fetch(`/api/groups/${currentChatId}/leave`, {
            method: 'POST',
            credentials: 'include'
        });

        const data = await response.json();

        if (data.code === 0) {
            showToast('已退出群聊', 'success');
            
            // 从聊天列表中移除
            const index = chats.findIndex(c => c.id === currentChatId);
            if (index > -1) {
                chats.splice(index, 1);
                renderChats();
            }

            // 返回聊天列表
            hideGroupChatDetail();
            hideChatDetail();
        } else {
            showToast(data.msg || '退出群聊失败', 'error');
        }
    } catch (error) {
        console.error('退出群聊错误:', error);
        showToast('网络连接异常，请重试', 'error');
    }
}

// 处理解散群聊
async function handleDissolveGroup() {
    if (!currentChatId) return;

    const result = await showConfirmDialog(
        '确认解散群聊？',
        '解散后群聊将被永久删除，所有成员都将被移出群聊，此操作无法撤销。'
    );

    if (!result) return;

    try {
        const response = await fetch(`/api/groups/${currentChatId}/dissolve`, {
            method: 'POST',
            credentials: 'include'
        });

        const data = await response.json();

        if (data.code === 0) {
            showToast('群聊已解散', 'success');
            
            // 从聊天列表中移除
            const index = chats.findIndex(c => c.id === currentChatId);
            if (index > -1) {
                chats.splice(index, 1);
                renderChats();
            }

            // 返回聊天列表
            hideGroupChatDetail();
            hideChatDetail();
        } else {
            showToast(data.msg || '解散群聊失败', 'error');
        }
    } catch (error) {
        console.error('解散群聊错误:', error);
        showToast('网络连接异常，请重试', 'error');
    }
}

// 显示添加成员界面
function showAddMember() {
    // 可以复用创建群聊的成员选择界面，或者创建专门的添加成员界面
    showToast('添加成员功能开发中', 'info');
}

// 确认对话框
function showConfirmDialog(title, message) {
    return new Promise((resolve) => {
        const dialog = document.createElement('div');
        dialog.className = 'confirm-dialog-overlay';
        dialog.innerHTML = `
            <div class="confirm-dialog">
                <div class="confirm-dialog-header">
                    <h3>${title}</h3>
                </div>
                <div class="confirm-dialog-body">
                    <p>${message}</p>
                </div>
                <div class="confirm-dialog-actions">
                    <button class="cancel-btn">取消</button>
                    <button class="confirm-btn danger">确认</button>
                </div>
            </div>
        `;

        // 添加样式
        dialog.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
        `;

        const dialogContent = dialog.querySelector('.confirm-dialog');
        dialogContent.style.cssText = `
            background: white;
            border-radius: 12px;
            padding: 0;
            max-width: 400px;
            width: 90%;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        `;

        const header = dialog.querySelector('.confirm-dialog-header');
        header.style.cssText = `
            padding: 20px 20px 0;
            text-align: center;
        `;

        const body = dialog.querySelector('.confirm-dialog-body');
        body.style.cssText = `
            padding: 15px 20px 20px;
            text-align: center;
            color: #666;
            line-height: 1.5;
        `;

        const actions = dialog.querySelector('.confirm-dialog-actions');
        actions.style.cssText = `
            display: flex;
            border-top: 1px solid #eee;
        `;

        const cancelBtn = dialog.querySelector('.cancel-btn');
        const confirmBtn = dialog.querySelector('.confirm-btn');

        const buttonStyle = `
            flex: 1;
            padding: 15px;
            border: none;
            background: transparent;
            cursor: pointer;
            font-size: 16px;
        `;

        cancelBtn.style.cssText = buttonStyle + `
            border-right: 1px solid #eee;
            color: #666;
        `;

        confirmBtn.style.cssText = buttonStyle + `
            color: #ef4444;
            font-weight: 500;
        `;

        // 事件处理
        cancelBtn.addEventListener('click', () => {
            document.body.removeChild(dialog);
            resolve(false);
        });

        confirmBtn.addEventListener('click', () => {
            document.body.removeChild(dialog);
            resolve(true);
        });

        // 点击遮罩关闭
        dialog.addEventListener('click', (e) => {
            if (e.target === dialog) {
                document.body.removeChild(dialog);
                resolve(false);
            }
        });

        document.body.appendChild(dialog);
    });
}

// 更新聊天详情页面以支持群聊显示
function updateChatDetailForGroup(chat) {
    const chatDetailName = document.getElementById('chat-detail-name');
    const chatMemberCount = document.getElementById('chat-member-count');
    
    if (chat.type === 'group') {
        if (chatDetailName) {
            chatDetailName.textContent = chat.name;
        }
        if (chatMemberCount) {
            chatMemberCount.textContent = `(${chat.members})`;
            chatMemberCount.style.display = 'block';
        }
    } else {
        if (chatDetailName) {
            chatDetailName.textContent = chat.name;
        }
        if (chatMemberCount) {
            chatMemberCount.style.display = 'none';
        }
    }
}

// 重写showChatDetail函数以支持群聊
const originalShowChatDetailFunc = window.showChatDetail;
window.showChatDetail = function(chatId, chatObject = null) {
    // 调用原始函数
    if (originalShowChatDetailFunc) {
        originalShowChatDetailFunc(chatId, chatObject);
    }

    // 更新群聊特定的显示
    const chat = chats.find(c => c.id === chatId);
    if (chat) {
        updateChatDetailForGroup(chat);
    }
};

// 导出群聊相关函数
window.initGroupChat = initGroupChat;
window.showCreateGroupChat = showCreateGroupChat;
window.hideCreateGroupChat = hideCreateGroupChat;
window.showGroupChatDetail = showGroupChatDetail;
window.hideGroupChatDetail = hideGroupChatDetail;
window.isGroupChat = isGroupChat;

// 在页面加载时初始化群聊功能
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(() => {
        initGroupChat();

        // 初始化全局WebSocket监听器（避免重复注册）
        if (window.wsManager && !window.wsManager._chatListenersInitialized) {
            initGlobalWebSocketListeners();
            window.wsManager._chatListenersInitialized = true;
            console.log('群聊WebSocket监听器初始化完成');
        }
    }, 500);
});

