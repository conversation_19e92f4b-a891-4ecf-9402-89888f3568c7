{"name": "wechat", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "一个仿微信的Web应用，包含朋友圈、聊天、相册和个人资料等功能。", "devDependencies": {"@playwright/test": "^1.54.2", "@stagewise/toolbar": "^0.2.1"}, "dependencies": {"acorn": "^8.14.1", "ansi-regex": "^6.1.0", "ansi-styles": "^6.2.1", "any-promise": "^1.3.0", "asynckit": "^0.4.0", "balanced-match": "^1.0.2", "brace-expansion": "^2.0.1", "bundle-require": "^5.1.0", "cac": "^6.7.14", "call-bind-apply-helpers": "^1.0.2", "chokidar": "^4.0.3", "clsx": "^2.1.1", "color-convert": "^2.0.1", "color-name": "^1.1.4", "combined-stream": "^1.0.8", "commander": "^4.1.1", "confbox": "^0.1.8", "consola": "^3.4.2", "copy-anything": "^3.0.5", "cross-spawn": "^7.0.6", "debug": "^4.4.1", "delayed-stream": "^1.0.0", "detect-europe-js": "^0.1.2", "detect-node-es": "^1.1.0", "dunder-proto": "^1.0.1", "eastasianwidth": "^0.2.0", "emoji-regex": "^9.2.2", "es-define-property": "^1.0.1", "es-errors": "^1.3.0", "es-object-atoms": "^1.1.1", "es-set-tostringtag": "^2.1.0", "esbuild": "^0.25.5", "fdir": "^6.4.4", "fix-dts-default-cjs-exports": "^1.0.1", "foreground-child": "^3.3.1", "form-data": "^4.0.2", "function-bind": "^1.1.2", "get-intrinsic": "^1.3.0", "get-nonce": "^1.0.1", "get-proto": "^1.0.1", "glob": "^10.4.5", "gopd": "^1.2.0", "has-symbols": "^1.1.0", "has-tostringtag": "^1.0.2", "hasown": "^2.0.2", "is-fullwidth-code-point": "^3.0.0", "is-standalone-pwa": "^0.1.1", "is-what": "^4.1.16", "isexe": "^2.0.0", "jackspeak": "^3.4.3", "javascript-time-ago": "^2.5.11", "joycon": "^3.1.1", "lilconfig": "^3.1.3", "lines-and-columns": "^1.2.4", "load-tsconfig": "^0.2.5", "lodash.sortby": "^4.7.0", "lru-cache": "^10.4.3", "lucide-react": "^0.503.0", "magic-string": "^0.30.17", "math-intrinsics": "^1.1.0", "mime-db": "^1.52.0", "mime-types": "^2.1.35", "minimatch": "^9.0.5", "minipass": "^7.1.2", "mlly": "^1.7.4", "ms": "^2.1.3", "mz": "^2.7.0", "nanoid": "^3.3.11", "node-fetch": "^2.7.0", "object-assign": "^4.1.1", "package-json-from-dist": "^1.0.1", "path-key": "^3.1.1", "path-scurry": "^1.11.1", "pathe": "^2.0.3", "picocolors": "^1.1.1", "picomatch": "^4.0.2", "pirates": "^4.0.7", "pkg-types": "^1.3.1", "postcss": "^8.5.3", "postcss-load-config": "^6.0.1", "postcss-prefix-selector": "^2.1.1", "preact": "^10.26.7", "punycode": "^2.3.1", "react": "^19.1.0", "react-dom": "^19.1.0", "react-remove-scroll": "^2.7.0", "react-remove-scroll-bar": "^2.3.8", "react-style-singleton": "^2.2.3", "readdirp": "^4.1.2", "relative-time-format": "^1.1.6", "resolve-from": "^5.0.0", "rollup": "^4.41.1", "scheduler": "^0.26.0", "shebang-command": "^2.0.0", "shebang-regex": "^3.0.0", "signal-exit": "^4.1.0", "source-map": "^0.8.0-beta.0", "source-map-js": "^1.2.1", "string-width": "^5.1.2", "string-width-cjs": "^4.2.3", "strip-ansi": "^7.1.0", "strip-ansi-cjs": "^6.0.1", "sucrase": "^3.35.0", "superjson": "^2.2.2", "tabbable": "^6.2.0", "tailwind-merge": "^3.3.0", "tailwindcss": "^4.1.7", "thenify": "^3.3.1", "thenify-all": "^1.6.0", "tinyexec": "^0.3.2", "tinyglobby": "^0.2.14", "tr46": "^1.0.1", "tree-kill": "^1.2.2", "ts-interface-checker": "^0.1.13", "tslib": "^2.8.1", "tsup": "^8.5.0", "ua-is-frozen": "^0.1.2", "ua-parser-js": "^2.0.3", "ufo": "^1.6.1", "undici-types": "^6.21.0", "use-callback-ref": "^1.3.3", "use-sidecar": "^1.1.3", "use-sync-external-store": "^1.5.0", "vite": "^6.3.5", "vite-plugin-css-injected-by-js": "^3.5.2", "webidl-conversions": "^4.0.2", "whatwg-url": "^7.1.0", "which": "^2.0.2", "wrap-ansi": "^8.1.0", "wrap-ansi-cjs": "^7.0.0", "zod": "^3.25.30", "zustand": "^5.0.5"}}