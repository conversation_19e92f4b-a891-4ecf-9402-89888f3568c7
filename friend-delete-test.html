<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>删除好友弹窗测试</title>
    <link rel="stylesheet" href="css/common.css">
    <link rel="stylesheet" href="css/contacts.css">
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .test-container {
            max-width: 500px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            padding: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        .test-title {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 24px;
            font-weight: 600;
        }
        
        .test-button {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            margin-bottom: 20px;
            transition: transform 0.2s ease;
        }
        
        .test-button:active {
            transform: scale(0.98);
        }

        .friend-item {
            display: flex;
            align-items: center;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 12px;
            margin-bottom: 10px;
            cursor: pointer;
            transition: background 0.2s ease;
        }

        .friend-item:hover {
            background: #e9ecef;
        }

        .friend-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            margin-right: 15px;
        }

        .friend-info {
            flex: 1;
        }

        .friend-name {
            font-size: 16px;
            font-weight: 500;
            color: #333;
            margin-bottom: 4px;
        }

        .friend-status {
            font-size: 14px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">删除好友弹窗测试</h1>
        
        <button class="test-button" onclick="testDeleteFriend('张三')">
            测试删除好友 - 张三
        </button>

        <button class="test-button" onclick="testDeleteFriend('李四')">
            测试删除好友 - 李四
        </button>

        <div class="friend-item" oncontextmenu="showDeleteMenu(event, {id: 1, name: '王五'})">
            <div class="friend-avatar">王</div>
            <div class="friend-info">
                <div class="friend-name">王五</div>
                <div class="friend-status">在线</div>
            </div>
        </div>

        <div class="friend-item" oncontextmenu="showDeleteMenu(event, {id: 2, name: '赵六'})">
            <div class="friend-avatar">赵</div>
            <div class="friend-info">
                <div class="friend-name">赵六</div>
                <div class="friend-status">离线</div>
            </div>
        </div>
    </div>

    <!-- 删除好友确认弹窗 - 大厂风格设计 -->
    <div class="friend-delete-confirm-modal" id="friend-delete-confirm-modal">
        <div class="friend-delete-confirm-container">
            <div class="friend-delete-confirm-content">
                <div class="friend-delete-confirm-icon">
                    <i class="fas fa-user-times"></i>
                </div>
                <h3 class="friend-delete-confirm-title">确认删除好友</h3>
                <p class="friend-delete-confirm-message">确定要删除好友 "<span id="friend-delete-name"></span>" 吗？删除后将无法查看对方朋友圈，请谨慎操作。</p>
                <div class="friend-delete-confirm-actions">
                    <button class="friend-delete-cancel-btn" onclick="cancelDeleteFriend()">
                        <i class="fas fa-times"></i>
                        取消
                    </button>
                    <button class="friend-delete-confirm-btn" onclick="confirmDeleteFriendModal()">
                        <i class="fas fa-user-minus"></i>
                        删除好友
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/js/all.min.js"></script>
    <script>
        // 全局变量存储要删除的好友信息
        let deletingFriend = null;

        // 测试删除好友
        function testDeleteFriend(name) {
            confirmDeleteFriend({id: Math.random(), name: name});
        }

        // 显示右键菜单
        function showDeleteMenu(event, friend) {
            event.preventDefault();
            confirmDeleteFriend(friend);
        }

        // 确认删除好友 - 显示精美弹窗
        function confirmDeleteFriend(contact) {
            deletingFriend = contact;
            
            // 设置好友名称
            const friendNameSpan = document.getElementById('friend-delete-name');
            if (friendNameSpan) {
                friendNameSpan.textContent = contact.name;
            }

            // 显示大厂风格确认弹窗
            const modal = document.getElementById('friend-delete-confirm-modal');
            if (modal) {
                modal.style.display = 'flex';
                // 强制重排
                modal.offsetHeight;
                setTimeout(() => {
                    modal.classList.add('active');
                }, 10);
            }
        }

        // 隐藏删除好友确认弹窗
        function hideFriendDeleteConfirmModal(immediate = false) {
            const modal = document.getElementById('friend-delete-confirm-modal');
            if (modal) {
                if (immediate) {
                    // 立即关闭，用于按钮点击后
                    modal.classList.add('closing');
                    setTimeout(() => {
                        modal.classList.remove('active', 'closing');
                        modal.style.display = 'none';
                    }, 250);
                } else {
                    // 正常关闭动画
                    modal.classList.remove('active');
                    setTimeout(() => {
                        modal.style.display = 'none';
                    }, 400);
                }
            }
            deletingFriend = null;
        }

        // 取消删除好友
        function cancelDeleteFriend() {
            // 添加按钮点击反馈
            const cancelBtn = document.querySelector('.friend-delete-cancel-btn');
            if (cancelBtn) {
                cancelBtn.classList.add('clicked');
            }
            
            hideFriendDeleteConfirmModal(true);
        }

        // 确认删除好友（从弹窗）
        function confirmDeleteFriendModal() {
            if (!deletingFriend) return;

            const friend = deletingFriend;
            
            // 添加按钮点击反馈
            const confirmBtn = document.querySelector('.friend-delete-confirm-btn');
            if (confirmBtn) {
                confirmBtn.classList.add('clicked');
            }

            // 快速关闭弹窗
            hideFriendDeleteConfirmModal(true);
            
            // 模拟删除操作
            alert(`好友 "${friend.name}" 已删除！`);
        }

        // 初始化删除好友确认弹窗事件监听
        function initFriendDeleteModal() {
            const modal = document.getElementById('friend-delete-confirm-modal');
            if (modal) {
                // 点击背景关闭弹窗
                modal.addEventListener('click', (e) => {
                    if (e.target === modal) {
                        hideFriendDeleteConfirmModal();
                    }
                });

                // 阻止弹窗内容区域的点击事件冒泡
                const container = modal.querySelector('.friend-delete-confirm-container');
                if (container) {
                    container.addEventListener('click', (e) => {
                        e.stopPropagation();
                    });
                }

                // ESC键关闭弹窗
                document.addEventListener('keydown', (e) => {
                    if (e.key === 'Escape' && modal.classList.contains('active')) {
                        hideFriendDeleteConfirmModal();
                    }
                });
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initFriendDeleteModal();
        });
    </script>
</body>
</html>
