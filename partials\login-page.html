<div class="login-page">
  <div class="login-container">
    <div class="login-header">
      <h1>微信</h1>
      <p>请登录您的账号</p>
    </div>
    
    <div class="login-form" id="login-form">
      <div class="form-group">
        <input type="text" id="login-username" placeholder="用户名" required>
      </div>
      <div class="form-group">
        <input type="password" id="login-password" placeholder="密码" required>
      </div>
      <button id="login-btn" class="login-btn">登录</button>
      <div class="form-switch">
        没有账号？<span id="show-register">立即注册</span>
      </div>
    </div>
    
    <div class="login-form" id="register-form" style="display:none;">
      <div class="form-group">
        <input type="text" id="register-username" placeholder="用户名" required>
      </div>
      <div class="form-group">
        <input type="password" id="register-password" placeholder="密码" required>
      </div>
      <button id="register-btn" class="register-btn">注册</button>
      <div class="form-switch">
        已有账号？<span id="show-login">立即登录</span>
      </div>
    </div>
    
    <div id="login-error" class="login-error"></div>
  </div>
</div>

<style>
.login-page {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
}

.login-container {
  background: #fff;
  border-radius: 16px;
  width: 360px;
  max-width: 90vw;
  box-shadow: 0 20px 40px rgba(0,0,0,0.1);
  padding: 40px 32px;
  text-align: center;
}

.login-header h1 {
  font-size: 32px;
  color: #333;
  margin: 0 0 8px 0;
  font-weight: 300;
}

.login-header p {
  color: #666;
  margin: 0 0 32px 0;
  font-size: 16px;
}

.form-group {
  margin-bottom: 20px;
  text-align: left;
}

.form-group input {
  width: 100%;
  padding: 16px;
  border: 2px solid #f0f0f0;
  border-radius: 12px;
  font-size: 16px;
  transition: border-color 0.3s ease;
  box-sizing: border-box;
}

.form-group input:focus {
  outline: none;
  border-color: #07c160;
}

.login-btn, .register-btn {
  width: 100%;
  padding: 16px;
  background: #07c160;
  color: #fff;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease;
  margin-bottom: 16px;
}

.login-btn:hover, .register-btn:hover {
  background: #06ad56;
}

.login-btn:disabled, .register-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.form-switch {
  text-align: center;
  font-size: 14px;
  color: #666;
}

.form-switch span {
  color: #07c160;
  cursor: pointer;
  font-weight: 500;
}

.form-switch span:hover {
  text-decoration: underline;
}

.login-error {
  color: #e64340;
  text-align: center;
  margin-top: 16px;
  font-size: 14px;
  min-height: 20px;
}

.login-error.success {
  color: #07c160;
}
</style>