# Git 使用指南

## 基础配置

### 初次使用Git配置
```bash
# 配置用户名和邮箱（全局配置）
git config --global user.name "你的用户名"
git config --global user.email "你的邮箱@example.com"

# 查看配置
git config --list
git config user.name
git config user.email
```

### 初始化仓库
```bash
# 在当前目录初始化Git仓库
git init

# 克隆远程仓库
git clone https://github.com/用户名/仓库名.git
```

## 查看状态和修改

### 查看文件状态
```bash
# 查看工作区状态（最常用）
git status

# 简洁格式查看状态
git status -s

# 查看修改的文件名
git diff --name-only
```

### 查看具体修改内容
```bash
# 查看工作区与暂存区的差异
git diff

# 查看指定文件的修改
git diff 文件名.txt

# 查看暂存区与最新提交的差异
git diff --cached

# 查看工作区与最新提交的差异
git diff HEAD
```

## 添加和提交文件

### 添加文件到暂存区
```bash
# 添加单个文件
git add 文件名.txt

# 添加所有修改的文件
git add .

# 添加所有文件（包括删除的文件）
git add -A

# 交互式添加（可选择性添加修改）
git add -i
```

### 提交到本地仓库
```bash
# 提交暂存区的文件到本地仓库
git commit -m "提交说明"

# 添加并提交（跳过暂存区）
git commit -am "提交说明"

# 修改最后一次提交信息
git commit --amend -m "新的提交说明"
```

## 恢复和撤销操作

### 恢复工作区修改
```bash
# 恢复单个文件的修改（未暂存）
git checkout -- 文件名.txt
git restore 文件名.txt  # Git 2.23+新命令

# 恢复所有修改的文件
git checkout -- .
git restore .  # Git 2.23+新命令
```

### 取消暂存
```bash
# 取消暂存单个文件
git reset HEAD 文件名.txt
git restore --staged 文件名.txt  # Git 2.23+新命令

# 取消所有暂存的文件
git reset HEAD
git restore --staged .  # Git 2.23+新命令
```

### 版本回退
```bash
# 回退到上一个版本（保留工作区修改）
git reset --soft HEAD~1

# 回退到上一个版本（清空暂存区，保留工作区）
git reset --mixed HEAD~1
git reset HEAD~1  # 默认是mixed模式

# 回退到上一个版本（完全重置，危险操作！）
git reset --hard HEAD~1

# 恢复文件到指定提交版本
git checkout 提交hash -- 文件名.txt
```

## 查看历史记录

### 查看提交历史
```bash
# 查看提交历史
git log

# 简洁格式查看提交历史
git log --oneline

# 查看最近n次提交
git log --oneline -5

# 查看文件的修改历史
git log --follow 文件名.txt

# 图形化显示分支历史
git log --graph --oneline
```

## 分支操作

### 分支管理
```bash
# 查看所有分支
git branch

# 创建新分支
git branch 分支名

# 切换分支
git checkout 分支名
git switch 分支名  # Git 2.23+新命令

# 创建并切换到新分支
git checkout -b 分支名
git switch -c 分支名  # Git 2.23+新命令

# 删除分支
git branch -d 分支名
```

## 远程仓库操作

### 远程仓库管理
```bash
# 查看远程仓库
git remote -v

# 添加远程仓库
git remote add origin https://github.com/用户名/仓库名.git

# 修改远程仓库地址
git remote set-url origin https://github.com/用户名/新仓库名.git
```

### 推送和拉取
```bash
# 推送到远程仓库
git push origin master
git push -u origin master  # 首次推送，设置上游分支

# 从远程仓库拉取
git pull origin master

# 获取远程仓库更新（不合并）
git fetch origin
```

## 实用技巧

### 暂存修改
```bash
# 暂存当前修改
git stash

# 查看暂存列表
git stash list

# 恢复最新的暂存
git stash pop

# 恢复指定的暂存
git stash apply stash@{0}

# 删除暂存
git stash drop stash@{0}
```

### 忽略文件
```bash
# 创建.gitignore文件
echo "node_modules/" >> .gitignore
echo "*.log" >> .gitignore

# 忽略已跟踪的文件
git rm --cached 文件名.txt
```

## 常用工作流程

### 日常开发流程
```bash
# 1. 查看当前状态
git status

# 2. 查看修改内容
git diff

# 3. 添加修改到暂存区
git add .

# 4. 提交到本地仓库
git commit -m "描述修改内容"

# 5. 推送到远程仓库
git push origin master
```

### 撤销修改流程
```bash
# 1. 查看状态
git status

# 2. 如果文件未暂存，直接恢复
git restore 文件名.txt

# 3. 如果文件已暂存，先取消暂存
git restore --staged 文件名.txt
# 然后恢复修改
git restore 文件名.txt
```

## 危险操作警告 ⚠️

以下命令会永久删除数据，使用前请三思：

```bash
# 硬重置（会丢失所有未提交的修改）
git reset --hard HEAD~1

# 强制推送（会覆盖远程仓库历史）
git push --force

# 删除未跟踪的文件
git clean -fd
```

## 快速参考

| 操作 | 命令 |
|------|------|
| 查看状态 | `git status` |
| 查看修改 | `git diff` |
| 添加文件 | `git add .` |
| 提交文件 | `git commit -m "说明"` |
| 推送远程 | `git push origin master` |
| 恢复修改 | `git restore 文件名` |
| 取消暂存 | `git restore --staged 文件名` |
| 查看历史 | `git log --oneline` |

记住：Git的核心是 **工作区 → 暂存区 → 本地仓库 → 远程仓库** 的流程！
