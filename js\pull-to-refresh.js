// 下拉刷新功能 - 增强版
class PullToRefresh {
    constructor(container, options = {}) {
        this.container = container;
        this.options = {
            threshold: 80, // 触发刷新的距离
            maxDistance: 120, // 最大下拉距离
            resistance: 2.5, // 阻力系数
            onRefresh: null, // 刷新回调函数
            refreshText: '下拉刷新',
            releaseText: '松开刷新',
            loadingText: '正在刷新...',
            completeText: '刷新完成',
            enableHapticFeedback: true, // 启用触觉反馈
            enableSoundFeedback: false, // 启用声音反馈
            smoothAnimation: true, // 启用平滑动画
            ...options
        };

        this.isRefreshing = false;
        this.startY = 0;
        this.currentY = 0;
        this.distance = 0;
        this.canPull = false;
        this.hasTriggeredHaptic = false; // 防止重复触发触觉反馈
        this.animationFrame = null; // 动画帧ID

        this.init();
    }

    init() {
        this.createRefreshIndicator();
        this.bindEvents();
    }

    createRefreshIndicator() {
        // 创建刷新指示器
        this.refreshIndicator = document.createElement('div');
        this.refreshIndicator.className = 'pull-refresh-indicator';
        this.refreshIndicator.innerHTML = `
            <div class="refresh-content">
                <div class="refresh-icon">
                    <i class="fas fa-arrow-down"></i>
                </div>
                <div class="refresh-text">${this.options.refreshText}</div>
            </div>
        `;

        // 插入到容器顶部
        this.container.insertBefore(this.refreshIndicator, this.container.firstChild);

        // 添加初始样式
        this.refreshIndicator.style.willChange = 'transform, opacity';
    }

    // 触觉反馈
    triggerHapticFeedback(type = 'light') {
        if (!this.options.enableHapticFeedback) return;

        try {
            if (navigator.vibrate) {
                // 不同类型的振动模式
                const patterns = {
                    light: [10],
                    medium: [20],
                    heavy: [30],
                    success: [10, 50, 10]
                };
                navigator.vibrate(patterns[type] || patterns.light);
            }
        } catch (error) {
            // 静默处理振动API错误
        }
    }

    // 平滑动画更新
    smoothUpdateIndicator() {
        if (this.animationFrame) {
            cancelAnimationFrame(this.animationFrame);
        }

        this.animationFrame = requestAnimationFrame(() => {
            this.updateIndicator();
        });
    }

    bindEvents() {
        // 触摸开始
        this.container.addEventListener('touchstart', (e) => {
            if (this.isRefreshing) return;
            
            // 检查是否可以下拉（页面在顶部）
            this.canPull = this.container.scrollTop === 0;
            
            if (this.canPull) {
                this.startY = e.touches[0].clientY;
                this.currentY = this.startY;
            }
        }, { passive: true });

        // 触摸移动
        this.container.addEventListener('touchmove', (e) => {
            if (this.isRefreshing || !this.canPull) return;

            this.currentY = e.touches[0].clientY;
            this.distance = Math.max(0, (this.currentY - this.startY) / this.options.resistance);

            // 限制最大距离，使用缓动函数
            if (this.distance > this.options.maxDistance) {
                const excess = this.distance - this.options.maxDistance;
                this.distance = this.options.maxDistance + excess * 0.3; // 超出部分阻力更大
            }

            if (this.distance > 0) {
                e.preventDefault(); // 防止页面滚动

                // 使用平滑动画更新
                if (this.options.smoothAnimation) {
                    this.smoothUpdateIndicator();
                } else {
                    this.updateIndicator();
                }

                // 触觉反馈 - 达到阈值时触发一次
                if (this.distance >= this.options.threshold && !this.hasTriggeredHaptic) {
                    this.triggerHapticFeedback('medium');
                    this.hasTriggeredHaptic = true;
                } else if (this.distance < this.options.threshold) {
                    this.hasTriggeredHaptic = false;
                }
            }
        }, { passive: false });

        // 触摸结束
        this.container.addEventListener('touchend', () => {
            if (this.isRefreshing || !this.canPull) return;

            // 清除动画帧
            if (this.animationFrame) {
                cancelAnimationFrame(this.animationFrame);
                this.animationFrame = null;
            }

            if (this.distance >= this.options.threshold) {
                this.triggerHapticFeedback('success');
                this.triggerRefresh();
            } else {
                this.resetIndicator();
            }

            this.canPull = false;
            this.hasTriggeredHaptic = false;
        }, { passive: true });

        // 鼠标事件（桌面端支持）
        let isMouseDown = false;
        
        this.container.addEventListener('mousedown', (e) => {
            if (this.isRefreshing) return;
            
            this.canPull = this.container.scrollTop === 0;
            
            if (this.canPull) {
                isMouseDown = true;
                this.startY = e.clientY;
                this.currentY = this.startY;
                this.container.style.userSelect = 'none';
            }
        });

        document.addEventListener('mousemove', (e) => {
            if (this.isRefreshing || !this.canPull || !isMouseDown) return;

            this.currentY = e.clientY;
            this.distance = Math.max(0, (this.currentY - this.startY) / this.options.resistance);
            this.distance = Math.min(this.distance, this.options.maxDistance);

            if (this.distance > 0) {
                e.preventDefault();
                this.updateIndicator();
            }
        });

        document.addEventListener('mouseup', () => {
            if (this.isRefreshing || !this.canPull || !isMouseDown) return;

            isMouseDown = false;
            this.container.style.userSelect = '';

            if (this.distance >= this.options.threshold) {
                this.triggerRefresh();
            } else {
                this.resetIndicator();
            }

            this.canPull = false;
        });
    }

    updateIndicator() {
        const progress = Math.min(this.distance / this.options.threshold, 1);
        const icon = this.refreshIndicator.querySelector('.refresh-icon i');
        const iconContainer = this.refreshIndicator.querySelector('.refresh-icon');
        const text = this.refreshIndicator.querySelector('.refresh-text');

        // 计算动态偏移量
        const baseOffset = 80; // 基础偏移量
        const dynamicOffset = Math.min(this.distance, baseOffset);

        // 更新位置 - 使用缓动函数
        const easeOutQuart = 1 - Math.pow(1 - progress, 4);
        this.refreshIndicator.style.transform = `translateY(${dynamicOffset - baseOffset}px)`;
        this.refreshIndicator.style.opacity = Math.min(progress * 1.2, 1);

        // 更新图标旋转和缩放
        if (progress >= 1) {
            icon.style.transform = 'rotate(180deg) scale(1.1)';
            iconContainer.style.transform = `scale(${1 + progress * 0.1})`;
            text.textContent = this.options.releaseText;
            this.refreshIndicator.classList.add('ready');
        } else {
            const rotation = progress * 180;
            const scale = 1 + progress * 0.05;
            icon.style.transform = `rotate(${rotation}deg) scale(${scale})`;
            iconContainer.style.transform = `scale(${1 + progress * 0.05})`;
            text.textContent = this.options.refreshText;
            this.refreshIndicator.classList.remove('ready');
        }

        // 添加弹性效果
        if (progress > 0.8) {
            const bounce = Math.sin((progress - 0.8) * 10) * 0.02;
            iconContainer.style.transform += ` translateY(${bounce * 10}px)`;
        }
    }

    triggerRefresh() {
        if (this.isRefreshing) return;

        this.isRefreshing = true;
        const icon = this.refreshIndicator.querySelector('.refresh-icon i');
        const iconContainer = this.refreshIndicator.querySelector('.refresh-icon');
        const text = this.refreshIndicator.querySelector('.refresh-text');

        // 显示加载状态 - 添加平滑过渡
        this.refreshIndicator.style.transition = 'all 0.4s cubic-bezier(0.16, 1, 0.3, 1)';
        this.refreshIndicator.style.transform = 'translateY(0px)';
        this.refreshIndicator.classList.add('loading');

        // 重置图标变换
        iconContainer.style.transform = 'scale(1)';
        icon.style.transform = 'scale(1)';
        icon.className = 'fas fa-spinner fa-spin';
        text.textContent = this.options.loadingText;

        // 执行刷新回调
        if (this.options.onRefresh) {
            Promise.resolve(this.options.onRefresh()).then(() => {
                this.completeRefresh();
            }).catch(() => {
                this.completeRefresh();
            });
        } else {
            setTimeout(() => {
                this.completeRefresh();
            }, 1000);
        }
    }

    completeRefresh() {
        const icon = this.refreshIndicator.querySelector('.refresh-icon i');
        const iconContainer = this.refreshIndicator.querySelector('.refresh-icon');
        const text = this.refreshIndicator.querySelector('.refresh-text');

        // 显示完成状态
        icon.className = 'fas fa-check';
        text.textContent = this.options.completeText;

        // 添加成功动画
        iconContainer.style.transform = 'scale(1.2)';
        setTimeout(() => {
            iconContainer.style.transform = 'scale(1)';
        }, 200);

        // 触觉反馈
        this.triggerHapticFeedback('success');

        setTimeout(() => {
            this.resetIndicator();
        }, 800);
    }

    resetIndicator() {
        const icon = this.refreshIndicator.querySelector('.refresh-icon i');
        const iconContainer = this.refreshIndicator.querySelector('.refresh-icon');
        const text = this.refreshIndicator.querySelector('.refresh-text');

        // 重置过渡效果
        this.refreshIndicator.style.transition = 'all 0.5s cubic-bezier(0.16, 1, 0.3, 1)';

        // 重置状态
        this.refreshIndicator.style.transform = 'translateY(-80px)';
        this.refreshIndicator.style.opacity = '0';
        this.refreshIndicator.classList.remove('ready', 'loading');

        // 重置图标
        icon.className = 'fas fa-arrow-down';
        icon.style.transform = 'rotate(0deg) scale(1)';
        iconContainer.style.transform = 'scale(1)';
        text.textContent = this.options.refreshText;

        // 清理状态
        this.distance = 0;
        this.isRefreshing = false;
        this.hasTriggeredHaptic = false;

        // 清除动画帧
        if (this.animationFrame) {
            cancelAnimationFrame(this.animationFrame);
            this.animationFrame = null;
        }

        // 延迟移除过渡效果，避免影响下次拖拽
        setTimeout(() => {
            this.refreshIndicator.style.transition = '';
        }, 500);
    }

    // 手动触发刷新
    refresh() {
        if (!this.isRefreshing) {
            this.triggerRefresh();
        }
    }

    // 销毁实例
    destroy() {
        if (this.refreshIndicator && this.refreshIndicator.parentNode) {
            this.refreshIndicator.parentNode.removeChild(this.refreshIndicator);
        }
    }
}

// 导出类
window.PullToRefresh = PullToRefresh;
