/* 聊天列表样式 */
.chat-list {
    padding: 0;
}



/* 空聊天提示 */
.empty-chat-message {
    text-align: center;
    padding: 60px 20px;
    color: var(--text-muted);
    font-size: 16px;
    line-height: 1.8;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-radius: var(--border-radius-lg);
    margin: 20px;
    box-shadow: var(--shadow-sm);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.empty-chat-message::before {
    content: '💬';
    display: block;
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.6;
}

.chat-item {
    display: flex;
    padding: 18px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    cursor: pointer;
    position: relative;
    transition: all var(--transition-fast);
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    margin: 8px 12px;
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-sm);
}

.chat-item:hover {
    background: rgba(255, 255, 255, 0.95);
    transform: translateY(-2px) scale(1.02);
    box-shadow: var(--shadow-md);
}

.chat-avatar {
    width: 54px;
    height: 54px;
    border-radius: var(--border-radius-md);
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    background-size: cover;
    background-position: center;
    margin-right: 16px;
    flex-shrink: 0;
    box-shadow: var(--shadow-md);
    position: relative;
    border: 2px solid rgba(255, 255, 255, 0.8);
    transition: all var(--transition-fast);
}

.chat-item:hover .chat-avatar {
    transform: scale(1.05);
    box-shadow: var(--shadow-lg);
}

.chat-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    overflow: hidden;
}

.chat-top {
    display: flex;
    justify-content: space-between;
    margin-bottom: 6px;
}

.chat-name {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
}

.chat-time {
    font-size: 12px;
    color: var(--dark-grey);
    margin-left: 10px;
    flex-shrink: 0;
}

.chat-message {
    font-size: 14px;
    color: var(--text-secondary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 80%;
    line-height: 1.4;
}

.chat-badge {
    background-color: var(--red);
    color: white;
    font-size: 12px;
    min-width: 20px;
    height: 20px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 6px;
    box-shadow: 0 2px 5px rgba(255, 59, 48, 0.3);
    font-weight: 600;
    position: absolute;
    top: 6px;
    left: 40px;
    animation: badgePulse 2s infinite;
    transform-origin: center;
    z-index: 5;
}

@keyframes badgePulse {
    0% {
        transform: scale(1);
    }
    10% {
        transform: scale(1.1);
    }
    20% {
        transform: scale(1);
    }
    100% {
        transform: scale(1);
    }
}

/* 聊天详情页样式 */
.chat-detail {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #fff;
    z-index: 200;
    max-width: 600px;
    margin: 0 auto;
    display: none;
    flex-direction: column;
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.3s ease;
}

.chat-detail.active {
    display: flex;
    opacity: 1;
    transform: translateY(0);
}

.chat-header {
    height: 60px;
    border-bottom: 1px solid var(--medium-grey);
    display: flex;
    align-items: center;
    padding: 0 15px;
    background-color: #fff;
    box-shadow: var(--shadow-sm);
}

.back-btn, .more-btn {
    background: none;
    border: none;
    font-size: 18px;
    color: var(--text-primary);
    cursor: pointer;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all var(--transition-fast);
}

.back-btn:hover, .more-btn:hover {
    background-color: var(--light-grey);
}

.chat-header h3 {
    flex: 1;
    text-align: center;
    font-size: 18px;
    font-weight: 600;
}

.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 15px;
    background-color: var(--light-grey);
    background-image: 
        radial-gradient(rgba(0, 0, 0, 0.03) 1px, transparent 1px),
        radial-gradient(rgba(0, 0, 0, 0.03) 1px, transparent 1px);
    background-size: 20px 20px;
    background-position: 0 0, 10px 10px;
    scroll-behavior: smooth;
}

/* 加载提示和错误提示 */
.loading-messages, .error-message, .empty-messages {
    text-align: center;
    padding: 30px 20px;
    color: var(--dark-grey);
    font-size: 15px;
    background-color: rgba(255, 255, 255, 0.7);
    border-radius: var(--border-radius-md);
    margin: 20px 0;
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    box-shadow: var(--shadow-sm);
}

.error-message {
    color: var(--red);
    background-color: rgba(255, 59, 48, 0.05);
}

/* 消息样式 */
.message {
    display: flex;
    margin-bottom: 20px;
    align-items: flex-start;
    position: relative;
    transition: transform 0.3s ease;
}

.message-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #eee;
    background-size: cover;
    background-position: center;
    margin-right: 12px;
    box-shadow: var(--shadow-sm);
    border: 2px solid #fff;
}

.message.sent {
    flex-direction: row-reverse;
}

.message.sent .message-avatar {
    margin-right: 0;
    margin-left: 12px;
}

.message-content {
    max-width: 70%;
    position: relative;
}

.message-bubble {
    padding: 14px 18px;
    font-size: 16px;
    word-break: break-word;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-fast);
    line-height: 1.5;
    position: relative;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.message.received .message-bubble {
    background: linear-gradient(135deg,
        rgba(224, 244, 255, 0.98) 0%,
        rgba(179, 229, 255, 0.95) 50%,
        rgba(135, 206, 235, 0.92) 100%);
    border-radius: 20px 20px 20px 6px;
    color: #1e293b;
    border: 1px solid rgba(135, 206, 235, 0.3);
    box-shadow:
        0 4px 12px rgba(135, 206, 235, 0.15),
        0 2px 6px rgba(135, 206, 235, 0.1),
        0 1px 3px rgba(0, 0, 0, 0.02),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
    position: relative;
    overflow: hidden;
}

.message.received .message-bubble::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: -9px;
    width: 18px;
    height: 18px;
    background: linear-gradient(135deg,
        rgba(179, 229, 255, 0.95) 0%,
        rgba(135, 206, 235, 0.92) 100%);
    border-radius: 0 0 18px 0;
    z-index: -1;
    border-right: 1px solid rgba(135, 206, 235, 0.3);
    border-bottom: 1px solid rgba(135, 206, 235, 0.3);
}

/* 添加好友消息气泡的微妙光泽效果 */
.message.received .message-bubble::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 50%;
    background: linear-gradient(180deg,
        rgba(255, 255, 255, 0.4) 0%,
        rgba(255, 255, 255, 0.1) 50%,
        transparent 100%);
    border-radius: 20px 20px 0 0;
    pointer-events: none;
}

/* 好友消息悬浮效果 */
.message.received:hover .message-bubble {
    background: linear-gradient(135deg,
        rgba(224, 244, 255, 1) 0%,
        rgba(179, 229, 255, 0.98) 50%,
        rgba(135, 206, 235, 0.95) 100%);
    box-shadow:
        0 6px 20px rgba(135, 206, 235, 0.25),
        0 3px 10px rgba(135, 206, 235, 0.15),
        0 1px 4px rgba(0, 0, 0, 0.04),
        inset 0 1px 0 rgba(255, 255, 255, 0.95);
    transform: translateY(-1px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 好友消息文字样式优化 */
.message.received .message-bubble {
    font-weight: 400;
    letter-spacing: 0.01em;
    line-height: 1.5;
}

/* 好友消息时间戳样式 */
.message.received .message-time {
    color: #64748b;
    font-size: 12px;
    opacity: 0.8;
}

.message.sent .message-bubble {
    background: var(--primary-gradient);
    color: white;
    border-radius: 20px 20px 6px 20px;
    box-shadow: var(--shadow-md), 0 0 20px rgba(7, 193, 96, 0.2);
}

.message.sent .message-bubble::before {
    content: '';
    position: absolute;
    bottom: 0;
    right: -9px;
    width: 18px;
    height: 18px;
    background: var(--primary-color);
    border-radius: 0 0 0 18px;
    z-index: -1;
}

.message-time {
    font-size: 12px;
    color: var(--dark-grey);
    margin-top: 5px;
    text-align: right;
    opacity: 0.8;
}

.message.received .message-time {
    text-align: left;
}

/* 未读消息提示条 */
.unread-indicator {
    position: absolute;
    bottom: 160px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(7, 193, 96, 0.95);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    color: white;
    padding: 10px 18px;
    border-radius: 24px;
    display: flex;
    align-items: center;
    gap: 12px;
    box-shadow: 0 6px 20px rgba(7, 193, 96, 0.4);
    border: 1px solid rgba(255, 255, 255, 0.3);
    z-index: 150;
    animation: slideUpFadeIn 0.3s ease-out;
    font-size: 14px;
    font-weight: 500;
    min-width: 140px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.unread-indicator:hover {
    background: rgba(7, 193, 96, 1);
    transform: translateX(-50%) translateY(-3px);
    box-shadow: 0 8px 25px rgba(7, 193, 96, 0.5);
}

.unread-indicator:active {
    transform: translateX(-50%) translateY(-1px);
    box-shadow: 0 4px 15px rgba(7, 193, 96, 0.4);
}

.unread-indicator-content {
    display: flex;
    align-items: center;
    gap: 4px;
}

.unread-count {
    font-weight: 600;
    font-size: 15px;
}

.unread-text {
    font-size: 13px;
    opacity: 0.9;
}

.unread-scroll-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    cursor: pointer;
    transition: all 0.2s ease;
}

.unread-scroll-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.unread-scroll-btn i {
    font-size: 12px;
}

@keyframes slideUpFadeIn {
    from {
        opacity: 0;
        transform: translateX(-50%) translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
}

/* 输入区域样式 - 现代化大厂风格 */
.chat-input-area {
    padding: 20px 16px 16px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 249, 250, 0.98) 100%);
    backdrop-filter: blur(30px);
    -webkit-backdrop-filter: blur(30px);
    border-top: 1px solid rgba(255, 255, 255, 0.3);
    display: flex;
    flex-direction: column;
    box-shadow:
        0 -8px 32px rgba(0, 0, 0, 0.08),
        0 -4px 16px rgba(0, 0, 0, 0.04),
        0 -2px 8px rgba(0, 0, 0, 0.02);
    border-radius: 20px 20px 0 0;
    border: 1px solid rgba(255, 255, 255, 0.4);
    border-bottom: none;
    position: relative;
}

/* 功能按钮栏 */
.chat-function-bar {
    display: flex;
    gap: 12px;
    margin-bottom: 16px;
    padding: 0 4px;
    justify-content: flex-start;
    align-items: center;
}

.function-btn {
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    border-radius: 16px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow:
        0 4px 12px rgba(0, 0, 0, 0.1),
        0 2px 6px rgba(0, 0, 0, 0.06),
        0 1px 3px rgba(0, 0, 0, 0.04);
    position: relative;
    overflow: hidden;
    border: none;
    flex-shrink: 0;
}

/* 表情按钮 - 默认橙色 */
.emoji-btn {
    background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
    color: white;
}

.emoji-btn:hover {
    background: linear-gradient(135deg, #ffb74d 0%, #ff9800 100%);
    transform: translateY(-3px) scale(1.1);
    box-shadow:
        0 8px 25px rgba(255, 152, 0, 0.4),
        0 4px 12px rgba(0, 0, 0, 0.15),
        0 2px 6px rgba(0, 0, 0, 0.1);
}

/* 图片按钮 - 默认紫色 */
.media-btn {
    background: linear-gradient(135deg, #9c27b0 0%, #7b1fa2 100%);
    color: white;
}

.media-btn:hover {
    background: linear-gradient(135deg, #ba68c8 0%, #9c27b0 100%);
    transform: translateY(-3px) scale(1.1);
    box-shadow:
        0 8px 25px rgba(156, 39, 176, 0.4),
        0 4px 12px rgba(0, 0, 0, 0.15),
        0 2px 6px rgba(0, 0, 0, 0.1);
}

/* 文件按钮 - 默认绿色 */
.file-btn {
    background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
    color: white;
}

.file-btn:hover {
    background: linear-gradient(135deg, #66bb6a 0%, #4caf50 100%);
    transform: translateY(-3px) scale(1.1);
    box-shadow:
        0 8px 25px rgba(76, 175, 80, 0.4),
        0 4px 12px rgba(0, 0, 0, 0.15),
        0 2px 6px rgba(0, 0, 0, 0.1);
}

.function-btn:active {
    transform: translateY(-1px) scale(1.05);
    transition: all 0.1s ease;
}

/* 添加按钮内部光晕效果 */
.function-btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
    border-radius: 50%;
    opacity: 0;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform: translate(-50%, -50%);
    pointer-events: none;
}

.function-btn:hover::before {
    width: 40px;
    height: 40px;
    opacity: 1;
}

/* 图标统一样式和动画 */
.function-btn i {
    font-size: 20px !important;
    line-height: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.function-btn:hover i {
    transform: scale(1.15);
}

/* 输入框容器 */
.chat-input-container {
    display: flex;
    align-items: flex-end;
    gap: 12px;
}

.input-wrapper {
    flex: 1;
    position: relative;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 249, 250, 0.95) 100%);
    border-radius: 24px;
    border: 2px solid transparent;
    box-shadow:
        0 4px 16px rgba(0, 0, 0, 0.08),
        0 2px 8px rgba(0, 0, 0, 0.04),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
}

/* 渐变边框效果 */
.input-wrapper::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(135deg,
        rgba(7, 193, 96, 0.3) 0%,
        rgba(76, 175, 80, 0.3) 50%,
        rgba(156, 39, 176, 0.3) 100%);
    border-radius: 26px;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

.input-wrapper:focus-within::before {
    opacity: 1;
}

.input-wrapper:focus-within {
    transform: translateY(-2px);
    box-shadow:
        0 8px 25px rgba(7, 193, 96, 0.15),
        0 4px 12px rgba(0, 0, 0, 0.08),
        0 2px 6px rgba(0, 0, 0, 0.04),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
}

#chat-input {
    width: 100%;
    border: none;
    background: transparent;
    padding: 16px 20px;
    font-size: 16px;
    resize: none;
    outline: none;
    min-height: 24px;
    max-height: 120px;
    line-height: 1.5;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    color: #333;
    overflow-y: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

#chat-input::-webkit-scrollbar {
    display: none;
}

#chat-input::placeholder {
    color: #999;
    font-weight: 400;
}



/* 发送按钮 */
.send-button {
    width: 52px;
    height: 52px;
    border-radius: 50%;
    background: linear-gradient(135deg, #07c160 0%, #4caf50 100%);
    color: white;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow:
        0 6px 20px rgba(7, 193, 96, 0.3),
        0 3px 10px rgba(0, 0, 0, 0.1),
        0 1px 4px rgba(0, 0, 0, 0.08);
    position: relative;
    overflow: hidden;
}

.send-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.send-button:hover::before {
    opacity: 1;
}

.send-button:hover {
    transform: scale(1.1) translateY(-2px);
    box-shadow:
        0 8px 30px rgba(7, 193, 96, 0.4),
        0 4px 15px rgba(0, 0, 0, 0.15),
        0 2px 8px rgba(0, 0, 0, 0.1);
}

.send-button:active {
    transform: scale(0.95) translateY(0);
    transition: all 0.1s ease;
}

.send-button:disabled {
    background: linear-gradient(135deg, #e0e0e0 0%, #bdbdbd 100%);
    color: #999;
    cursor: not-allowed;
    box-shadow:
        0 2px 8px rgba(0, 0, 0, 0.1),
        0 1px 4px rgba(0, 0, 0, 0.06);
}

.send-button:disabled:hover {
    transform: none;
    box-shadow:
        0 2px 8px rgba(0, 0, 0, 0.1),
        0 1px 4px rgba(0, 0, 0, 0.06);
}

.send-button i {
    font-size: 18px;
    margin-left: 2px;
}

/* 图片和视频消息样式增强 */
.image-container {
    position: relative;
    display: inline-block;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.image-container:hover {
    transform: scale(1.03);
}

.message-image {
    max-width: 240px;
    max-height: 300px;
    border-radius: 12px;
    cursor: pointer;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-fast);
    border: 2px solid #fff;
    display: block;
}

.video-container {
    position: relative;
    display: inline-block;
    transition: all var(--transition-fast);
}

.video-container:hover {
    transform: scale(1.03);
}

.message-video {
    max-width: 240px;
    max-height: 300px;
    border-radius: 12px;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-fast);
    border: 2px solid #fff;
    display: block;
}

/* 视频全屏按钮样式 */
.video-fullscreen-overlay,
.video-fullscreen-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    width: 40px;
    height: 40px;
    background-color: rgba(0, 0, 0, 0.6);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 10;
    transition: all 0.3s ease;
    backdrop-filter: blur(4px);
}

.video-fullscreen-overlay:hover,
.video-fullscreen-btn:hover {
    background-color: rgba(0, 0, 0, 0.8);
    transform: scale(1.1);
}

.video-fullscreen-overlay i,
.video-fullscreen-btn i {
    color: white;
    font-size: 16px;
}

/* 文件消息样式 */
.file-message {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 12px;
    padding: 0;
    overflow: hidden;
    border: 1px solid rgba(0, 0, 0, 0.1);
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-fast);
}

.file-message:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.file-info {
    display: flex;
    align-items: center;
    padding: 16px;
    gap: 12px;
    position: relative;
}

.file-info.uploading {
    opacity: 0.7;
}

.file-info.uploading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.file-download {
    margin-left: auto;
    padding: 8px;
    border-radius: 50%;
    background: rgba(7, 193, 96, 0.1);
    transition: all var(--transition-fast);
}

.file-download:hover {
    background: rgba(7, 193, 96, 0.2);
    transform: scale(1.1);
}

.file-download a {
    color: var(--primary-color);
    text-decoration: none;
    font-size: 16px;
}

/* 上传中的媒体样式 */
.message-image.uploading,
.message-video.uploading {
    opacity: 0.7;
    position: relative;
}

.message-image.uploading::after,
.message-video.uploading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 消息状态样式 */
.message-status.uploading {
    color: #ffa500;
}

.message-status.uploading i {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.file-icon {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    flex-shrink: 0;
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

.file-details {
    flex: 1;
    min-width: 0;
}

.file-name {
    font-size: 15px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 4px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.file-size {
    font-size: 13px;
    color: var(--text-secondary);
    opacity: 0.8;
}

/* 表情选择器 - 现代化设计 */
.emoji-picker {
    position: absolute;
    bottom: 100%;
    left: 0;
    right: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 249, 250, 0.98) 100%);
    backdrop-filter: blur(30px);
    -webkit-backdrop-filter: blur(30px);
    border-radius: 20px;
    box-shadow:
        0 -8px 32px rgba(0, 0, 0, 0.12),
        0 -4px 16px rgba(0, 0, 0, 0.08),
        0 -2px 8px rgba(0, 0, 0, 0.04);
    z-index: 1000;
    display: none;
    border: 2px solid transparent;
    margin-bottom: 8px;
    max-height: 350px;
    overflow: hidden;
}

.emoji-picker.active {
    display: block;
}

/* 表情选择器头部 - 隐藏分类按钮 */
.emoji-picker-header {
    display: none;
}

.emoji-categories {
    display: flex;
    gap: 8px;
    justify-content: space-around;
}

.emoji-category {
    width: 36px;
    height: 36px;
    border-radius: 12px;
    background: transparent;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 16px;
    color: #666;
    transition: all 0.3s ease;
    position: relative;
}

.emoji-category:hover {
    background: rgba(7, 193, 96, 0.1);
    color: #07c160;
    transform: scale(1.1);
}

.emoji-category.active {
    background: linear-gradient(135deg, #07c160 0%, #4caf50 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(7, 193, 96, 0.3);
}

/* 表情内容区域 */
.emoji-picker-content {
    padding: 16px;
    display: grid;
    grid-template-columns: repeat(8, 1fr);
    gap: 8px;
    max-height: 240px;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: rgba(7, 193, 96, 0.3) transparent;
}

.emoji-picker-content::-webkit-scrollbar {
    width: 6px;
}

.emoji-picker-content::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.05);
    border-radius: 3px;
}

.emoji-picker-content::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #07c160 0%, #4caf50 100%);
    border-radius: 3px;
    transition: background 0.3s ease;
}

.emoji-picker-content::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #06a84d 0%, #43a047 100%);
}

.emoji-item {
    width: 36px;
    height: 36px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 24px;
    cursor: pointer;
    border-radius: 12px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    user-select: none;
    position: relative;
    background: transparent;
}

.emoji-item::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(135deg,
        rgba(255, 193, 7, 0.4) 0%,
        rgba(255, 152, 0, 0.4) 50%,
        rgba(156, 39, 176, 0.4) 100%);
    border-radius: 14px;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

.emoji-item:hover::before {
    opacity: 1;
}

.emoji-item:hover {
    background: linear-gradient(135deg, #fff8e1 0%, #ffecb3 100%);
    transform: translateY(-2px) scale(1.2);
    box-shadow:
        0 8px 20px rgba(243, 156, 18, 0.2),
        0 4px 12px rgba(0, 0, 0, 0.1),
        0 2px 6px rgba(0, 0, 0, 0.06);
}

.emoji-item:active {
    transform: translateY(0) scale(1.1);
    transition: all 0.1s ease;
}

/* 评论栏按钮样式 */
.comment-bar-actions {
    display: flex;
    align-items: center;
    margin-top: 8px;
}

.emoji-btn {
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    padding: 5px 10px;
    opacity: 0.7;
    transition: opacity 0.2s;
    border-radius: 4px;
}

.emoji-btn:hover {
    opacity: 1;
    background-color: #f5f5f5;
}

.send-btn, .cancel-btn {
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    border: none;
    margin-left: 8px;
}

.send-btn {
    background-color: #07c160;
    color: white;
    box-shadow: 0 2px 4px rgba(7, 193, 96, 0.3);
}

.send-btn:hover:not([disabled]) {
    background-color: #06b457;
    transform: translateY(-1px);
    box-shadow: 0 3px 6px rgba(7, 193, 96, 0.4);
}

.send-btn[disabled] {
    opacity: 0.5;
    cursor: not-allowed;
}

.cancel-btn {
    background-color: #f5f5f5;
    color: #666;
    border: 1px solid #ddd;
}

.cancel-btn:hover {
    background-color: #eee;
    color: #333;
}

/* 媒体预览弹窗 */
.media-preview-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 99999;
    backdrop-filter: blur(4px);
    animation: modalFadeIn 0.3s ease;
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.media-preview-content {
    max-width: 95%;
    max-height: 95%;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
}

.media-preview-content img,
.media-preview-content video {
    max-width: 100%;
    max-height: 90vh;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
    object-fit: contain;
}

.media-close-btn {
    position: absolute;
    top: 20px;
    right: 20px;
    width: 50px;
    height: 50px;
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 100000;
    transition: all 0.3s ease;
    backdrop-filter: blur(4px);
    color: white;
    font-size: 40px;
    font-weight: normal;
    line-height: 1;
}

.media-close-btn:hover {
    background-color: rgba(255, 255, 255, 0.2);
    transform: scale(1.1);
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}



.empty-message {
    text-align: center;
    padding: 30px;
    color: #999;
    font-size: 14px;
}

/* 聊天详情页面的表情选择器样式 */
.chat-detail .emoji-picker {
    position: absolute;
    bottom: 100%;
    left: 0;
    right: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 249, 250, 0.98) 100%);
    backdrop-filter: blur(30px);
    -webkit-backdrop-filter: blur(30px);
    border-radius: 20px;
    box-shadow:
        0 -8px 32px rgba(0, 0, 0, 0.12),
        0 -4px 16px rgba(0, 0, 0, 0.08),
        0 -2px 8px rgba(0, 0, 0, 0.04);
    z-index: 1000;
    display: none;
    border: 2px solid transparent;
    margin-bottom: 8px;
    max-height: 350px;
    overflow: hidden;
}

.chat-detail .emoji-picker.active {
    display: block !important;
}

/* 修复表情选择器定位问题 */
#emoji-picker.active,
.chat-detail .emoji-picker.active {
    display: block !important;
    /* 修复定位 */
    position: absolute !important;
    bottom: 100% !important;
    left: 0 !important;
    right: 0 !important;
    max-width: 100% !important;
    margin-bottom: 8px !important;
    z-index: 1000 !important;
}

/* 确保在所有情况下都隐藏表情分类头部 */
.emoji-picker-header,
.chat-detail .emoji-picker-header,
#emoji-picker .emoji-picker-header {
    display: none !important;
}

/* 聊天右键菜单样式 */
.chat-context-menu {
    position: absolute;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-radius: var(--border-radius-md);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.3);
    z-index: 10000;
    min-width: 140px;
    overflow: hidden;
    animation: contextMenuShow 0.2s ease-out;
}

.chat-context-menu-item {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    cursor: pointer;
    transition: all var(--transition-fast);
    color: var(--text-primary);
    font-size: 14px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.chat-context-menu-item:last-child {
    border-bottom: none;
}

.chat-context-menu-item:hover {
    background: rgba(220, 53, 69, 0.1);
    color: #dc3545;
}

.chat-context-menu-item i {
    margin-right: 8px;
    font-size: 14px;
    width: 16px;
    text-align: center;
}

@keyframes contextMenuShow {
    from {
        opacity: 0;
        transform: scale(0.9) translateY(-10px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

/* ==================== WebSocket 相关样式 ==================== */

/* 输入状态指示器 */
.typing-indicator {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    margin: 8px 0;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 18px;
    max-width: 200px;
    animation: fadeInUp 0.3s ease-out;
}

.typing-dots {
    display: flex;
    align-items: center;
    margin-right: 8px;
}

.typing-dots span {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: var(--primary-color);
    margin: 0 2px;
    animation: typingDots 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) {
    animation-delay: -0.32s;
}

.typing-dots span:nth-child(2) {
    animation-delay: -0.16s;
}

.typing-text {
    font-size: 12px;
    color: var(--text-muted);
    font-style: italic;
}

@keyframes typingDots {
    0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* WebSocket连接状态指示器 */
.websocket-status {
    position: fixed;
    top: 10px;
    right: 10px;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    z-index: 1000;
    transition: all 0.3s ease;
}

.websocket-status.connected {
    background: rgba(76, 175, 80, 0.9);
    color: white;
}

.websocket-status.disconnected {
    background: rgba(244, 67, 54, 0.9);
    color: white;
}

.websocket-status.connecting {
    background: rgba(255, 193, 7, 0.9);
    color: white;
}

/* 在线状态指示器 */
.online-indicator {
    position: absolute;
    bottom: 2px;
    right: 2px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid white;
    background: #4CAF50;
    box-shadow: 0 0 4px rgba(0, 0, 0, 0.2);
}

.online-indicator.offline {
    background: #9E9E9E;
}

/* 消息状态增强样式 */
.message-status.read {
    color: #4CAF50;
}

.message-status.sent {
    color: #9E9E9E;
}

.message-status.sending {
    color: #FF9800;
}

.message-status.failed {
    color: #F44336;
    cursor: pointer;
}

/* 群聊消息已读状态样式 */
.message-status.group-read {
    color: #4CAF50;
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 4px;
}

.message-status.group-read:hover {
    background: rgba(76, 175, 80, 0.1);
    border-radius: 4px;
    padding: 2px 4px;
}

/* 群聊已读详情弹窗样式 */
.group-read-modal {
    animation: fadeIn 0.3s ease;
}

.group-read-content {
    animation: slideUp 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 移动端聊天滑动删除样式 */
.chat-item.swipe-delete-ready-chat {
    background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
    box-shadow:
        0 4px 16px rgba(245, 158, 11, 0.2),
        0 2px 8px rgba(245, 158, 11, 0.1);
}

.swipe-delete-button-chat {
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    width: 90px;
    background: linear-gradient(135deg, #ff4757 0%, #ff3742 100%);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    font-weight: 600;
    transform: translateX(100%);
    transition: transform 0.3s ease;
    border-radius: 0 16px 16px 0;
    z-index: 10;
    cursor: pointer;
    box-shadow:
        -2px 0 8px rgba(255, 71, 87, 0.3),
        0 2px 8px rgba(0, 0, 0, 0.1);
    min-width: 90px;
    text-align: center;
    white-space: nowrap;
}

.swipe-delete-button-chat:hover {
    background: linear-gradient(135deg, #ff3742 0%, #ff2730 100%);
}

.swipe-delete-button-chat:active {
    transform: translateX(0) scale(0.95);
}

/* 触摸设备专用优化 */
@media (hover: none) and (pointer: coarse) {
    .chat-item {
        /* 为触摸设备优化触摸目标 */
        -webkit-tap-highlight-color: transparent;
        tap-highlight-color: transparent;
        user-select: none;
        -webkit-user-select: none;
        -webkit-touch-callout: none;
    }

    /* 禁用触摸设备上的hover效果 */
    .chat-item:hover {
        background: rgba(255, 255, 255, 0.95);
        transform: translateY(-2px) scale(1.02);
        box-shadow: var(--shadow-md);
    }

    .swipe-delete-button-chat:hover {
        background: linear-gradient(135deg, #ff4757 0%, #ff3742 100%);
    }
}

/* 移动端响应式优化 */
@media (max-width: 480px) {
    .chat-item {
        /* 增加移动端的触摸目标尺寸 */
        min-height: 70px;
        padding: 16px 18px;
        margin: 6px 12px;
    }

    .swipe-delete-button-chat {
        width: 85px;
        min-width: 85px;
        font-size: 16px;
    }
}

/* 移动端聊天操作提示 */
.mobile-chat-tips {
    position: fixed;
    bottom: 80px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 12px;
    z-index: 1000;
    animation: tipsFadeIn 0.3s ease-out;
    backdrop-filter: blur(10px);
}

.mobile-chat-tips .tips-content {
    display: flex;
    align-items: center;
    gap: 6px;
}

.mobile-chat-tips i {
    font-size: 14px;
    color: #fbbf24;
}

@keyframes tipsFadeIn {
    from {
        opacity: 0;
        transform: translateX(-50%) translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
}

@media (max-width: 480px) {
    .mobile-chat-tips {
        bottom: 70px;
        font-size: 11px;
        padding: 6px 12px;
    }
}


/* ===== 群聊功能样式 ===== */

/* 群聊按钮样式 */
.group-chat-btn {
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    background: linear-gradient(135deg, var(--primary-color) 0%, #06ae56 100%);
    color: white;
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all var(--transition-fast);
    box-shadow: var(--shadow-sm);
    font-size: 16px;
}

.group-chat-btn:hover {
    transform: translateY(-50%) scale(1.1);
    box-shadow: var(--shadow-md);
}

.group-chat-btn:active {
    transform: translateY(-50%) scale(0.95);
}

/* 聊天标题容器（支持群聊显示） */
.chat-title-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
}

.chat-title-container h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
}

.member-count {
    font-size: 12px;
    color: var(--text-muted);
    margin-top: 2px;
}

/* 创建群聊页面样式 */
.create-group-chat {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    z-index: 1000;
    transform: translateX(100%);
    transition: transform var(--transition-smooth);
    overflow-y: auto;
}

.create-group-chat.active {
    transform: translateX(0);
}

.group-chat-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(20px);
    box-shadow: var(--shadow-sm);
    position: sticky;
    top: 0;
    z-index: 10;
}

.group-chat-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.confirm-btn {
    background: var(--primary-gradient);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 16px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
    box-shadow: var(--shadow-sm);
}

.confirm-btn:disabled {
    background: #e2e8f0;
    color: #94a3b8;
    cursor: not-allowed;
}

.confirm-btn:not(:disabled):hover {
    transform: scale(1.05);
    box-shadow: var(--shadow-md);
}

/* 群聊信息区域 */
.group-info-section {
    padding: 30px 20px;
    text-align: center;
    background: rgba(255, 255, 255, 0.8);
    margin: 20px;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    backdrop-filter: blur(10px);
}

.group-avatar-container {
    position: relative;
    display: inline-block;
    margin-bottom: 20px;
}

.group-avatar-preview {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: var(--primary-gradient);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 32px;
    margin: 0 auto;
    box-shadow: var(--shadow-md);
    background-size: cover;
    background-position: center;
}

.change-avatar-btn {
    position: absolute;
    bottom: -5px;
    right: -5px;
    width: 28px;
    height: 28px;
    background: #fff;
    border: 2px solid var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 12px;
    color: var(--primary-color);
    transition: all var(--transition-fast);
}

.change-avatar-btn:hover {
    transform: scale(1.1);
    background: var(--primary-color);
    color: white;
}

.group-name-input-container {
    position: relative;
}

.group-name-input-container input {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e2e8f0;
    border-radius: var(--border-radius-md);
    font-size: 16px;
    text-align: center;
    background: rgba(255, 255, 255, 0.9);
    transition: all var(--transition-fast);
}

.group-name-input-container input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(7, 193, 96, 0.1);
    outline: none;
}

.input-counter {
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 12px;
    color: var(--text-muted);
}

/* 成员选择区域 */
.member-selection-section {
    flex: 1;
    background: rgba(255, 255, 255, 0.8);
    margin: 0 20px 20px;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    backdrop-filter: blur(10px);
    overflow: hidden;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #e2e8f0;
}

.section-header h4 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
}

.selected-count {
    font-size: 14px;
    color: var(--primary-color);
    font-weight: 500;
}

.selected-members {
    padding: 15px 20px;
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    min-height: 50px;
    align-items: center;
    border-bottom: 1px solid #e2e8f0;
}

.selected-member {
    display: flex;
    align-items: center;
    background: var(--primary-gradient);
    color: white;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    animation: slideIn 0.3s ease-out;
}

.selected-member .remove-btn {
    margin-left: 8px;
    cursor: pointer;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    transition: all var(--transition-fast);
}

.selected-member .remove-btn:hover {
    background: rgba(255, 255, 255, 0.5);
    transform: scale(1.1);
}

.friends-list-container {
    max-height: 400px;
    overflow-y: auto;
}

.search-container {
    position: relative;
    padding: 15px 20px;
    border-bottom: 1px solid #e2e8f0;
}

.search-container input {
    width: 100%;
    padding: 10px 40px 10px 16px;
    border: 1px solid #e2e8f0;
    border-radius: var(--border-radius-md);
    font-size: 14px;
    background: rgba(255, 255, 255, 0.9);
    transition: all var(--transition-fast);
}

.search-container input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(7, 193, 96, 0.1);
    outline: none;
}

.search-container i {
    position: absolute;
    right: 35px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-muted);
}

.friend-item {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    cursor: pointer;
    transition: all var(--transition-fast);
    border-bottom: 1px solid rgba(226, 232, 240, 0.5);
}

.friend-item:hover {
    background: rgba(7, 193, 96, 0.05);
}

.friend-item.selected {
    background: rgba(7, 193, 96, 0.1);
}

.friend-item .friend-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--primary-gradient);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    margin-right: 12px;
    background-size: cover;
    background-position: center;
}

.friend-item .friend-info {
    flex: 1;
}

.friend-item .friend-name {
    font-size: 16px;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 2px;
}

.friend-item .friend-status {
    font-size: 12px;
    color: var(--text-muted);
}

.friend-item .selection-indicator {
    width: 20px;
    height: 20px;
    border: 2px solid #e2e8f0;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-fast);
}

.friend-item.selected .selection-indicator {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

/* 无好友提示样式 */
.no-friends-message {
    text-align: center;
    padding: 40px 20px;
    color: var(--text-muted);
    font-size: 14px;
    background: rgba(255, 255, 255, 0.5);
    border-radius: var(--border-radius-md);
    margin: 20px;
}

.no-friends-message::before {
    content: '👥';
    display: block;
    font-size: 32px;
    margin-bottom: 12px;
    opacity: 0.6;
}

/* 群聊详情页面样式 */
.group-chat-detail {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    z-index: 1000;
    transform: translateX(100%);
    transition: transform var(--transition-smooth);
    overflow-y: auto;
}

.group-chat-detail.active {
    transform: translateX(0);
}

.group-detail-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(20px);
    box-shadow: var(--shadow-sm);
    position: sticky;
    top: 0;
    z-index: 10;
}

.group-detail-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.group-more-btn {
    width: 40px;
    height: 40px;
    border: none;
    background: transparent;
    color: var(--text-primary);
    font-size: 18px;
    cursor: pointer;
    border-radius: 50%;
    transition: all var(--transition-fast);
}

.group-more-btn:hover {
    background: rgba(0, 0, 0, 0.05);
}

/* 群聊信息卡片 */
.group-info-card {
    display: flex;
    align-items: center;
    padding: 30px 20px;
    background: rgba(255, 255, 255, 0.8);
    margin: 20px;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    backdrop-filter: blur(10px);
}

.group-avatar-large {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: var(--primary-gradient);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
    margin-right: 16px;
    box-shadow: var(--shadow-md);
    background-size: cover;
    background-position: center;
}

.group-basic-info h4 {
    margin: 0 0 4px 0;
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
}

.group-member-count {
    font-size: 14px;
    color: var(--text-muted);
}

/* 群成员区域 */
.group-members-section {
    background: rgba(255, 255, 255, 0.8);
    margin: 0 20px 20px;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    backdrop-filter: blur(10px);
    overflow: hidden;
}

.section-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #e2e8f0;
}

.section-title h5 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
}

.add-member-btn {
    width: 32px;
    height: 32px;
    border: 2px dashed var(--primary-color);
    background: transparent;
    color: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all var(--transition-fast);
    font-size: 14px;
}

.add-member-btn:hover {
    background: var(--primary-color);
    color: white;
    transform: scale(1.1);
}

.group-members-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
    gap: 16px;
    padding: 20px;
}

.member-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    cursor: pointer;
    transition: all var(--transition-fast);
    padding: 8px;
    border-radius: var(--border-radius-md);
}

.member-item:hover {
    background: rgba(7, 193, 96, 0.05);
    transform: translateY(-2px);
}

.member-item .member-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: var(--primary-gradient);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    margin-bottom: 8px;
    box-shadow: var(--shadow-sm);
    background-size: cover;
    background-position: center;
}

.member-item .member-name {
    font-size: 12px;
    color: var(--text-primary);
    font-weight: 500;
    max-width: 70px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.member-item .owner-badge {
    font-size: 10px;
    color: #f59e0b;
    margin-top: 2px;
}

/* 群聊操作区域 */
.group-actions-section {
    background: rgba(255, 255, 255, 0.8);
    margin: 0 20px 20px;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    backdrop-filter: blur(10px);
    overflow: hidden;
}

.action-item {
    display: flex;
    align-items: center;
    padding: 16px 20px;
    cursor: pointer;
    transition: all var(--transition-fast);
    border-bottom: 1px solid rgba(226, 232, 240, 0.5);
}

.action-item:last-child {
    border-bottom: none;
}

.action-item:hover {
    background: rgba(0, 0, 0, 0.02);
}

.action-item.danger-action:hover {
    background: rgba(239, 68, 68, 0.05);
}

.action-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #f1f5f9;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 16px;
    color: var(--text-primary);
}

.danger-action .action-icon {
    background: rgba(239, 68, 68, 0.1);
    color: #ef4444;
}

.action-content {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.action-title {
    font-size: 16px;
    font-weight: 500;
    color: var(--text-primary);
}

.danger-action .action-title {
    color: #ef4444;
}

.action-content i {
    color: var(--text-muted);
}

/* 切换开关样式 */
.toggle-switch {
    position: relative;
    display: inline-block;
    width: 44px;
    height: 24px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-switch label {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #cbd5e1;
    transition: var(--transition-fast);
    border-radius: 12px;
}

.toggle-switch label:before {
    position: absolute;
    content: "";
    height: 20px;
    width: 20px;
    left: 2px;
    bottom: 2px;
    background-color: white;
    transition: var(--transition-fast);
    border-radius: 50%;
    box-shadow: var(--shadow-sm);
}

.toggle-switch input:checked + label {
    background-color: var(--primary-color);
}

.toggle-switch input:checked + label:before {
    transform: translateX(20px);
}

/* 动画效果 */
@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 群聊特殊样式标识 */
.chat-item.group-chat .chat-avatar {
    border: 2px solid var(--primary-color);
}

.chat-item.group-chat .chat-name::after {
    content: '👥';
    margin-left: 6px;
    font-size: 12px;
}

/* 移动端适配 */
@media (max-width: 480px) {
    .group-chat-btn {
        width: 36px;
        height: 36px;
        font-size: 14px;
        right: 16px;
    }
    
    .group-info-section {
        margin: 15px;
        padding: 20px 15px;
    }
    
    .member-selection-section {
        margin: 0 15px 15px;
    }
    
    .group-info-card {
        margin: 15px;
        padding: 20px 15px;
    }
    
    .group-members-section {
        margin: 0 15px 15px;
    }
    
    .group-actions-section {
        margin: 0 15px 15px;
    }
    
    .group-members-grid {
        grid-template-columns: repeat(auto-fill, minmax(70px, 1fr));
        gap: 12px;
        padding: 15px;
    }
    
    .member-item .member-avatar {
        width: 45px;
        height: 45px;
    }
    
    /* 移动端创建群聊页面优化 */
    .create-group-chat {
        padding-bottom: 20px;
    }
    
    .group-chat-header {
        padding: 15px;
    }
    
    .group-avatar-preview {
        width: 70px;
        height: 70px;
        font-size: 28px;
    }
    
    .change-avatar-btn {
        width: 24px;
        height: 24px;
        font-size: 10px;
        bottom: -3px;
        right: -3px;
    }
    
    .selected-members {
        padding: 12px 15px;
        min-height: 45px;
    }
    
    .selected-member {
        padding: 5px 10px;
        font-size: 11px;
    }
    
    .selected-member .remove-btn {
        width: 14px;
        height: 14px;
        font-size: 9px;
        margin-left: 6px;
    }
    
    .friend-item {
        padding: 10px 15px;
    }
    
    .friend-item .friend-avatar {
        width: 35px;
        height: 35px;
        margin-right: 10px;
    }
    
    .friend-item .friend-name {
        font-size: 15px;
    }
    
    .friend-item .friend-status {
        font-size: 11px;
    }
    
    .friend-item .selection-indicator {
        width: 18px;
        height: 18px;
    }
    
    /* 群聊详情页面移动端优化 */
    .group-detail-header {
        padding: 15px;
    }
    
    .group-avatar-large {
        width: 55px;
        height: 55px;
        font-size: 22px;
        margin-right: 12px;
    }
    
    .group-basic-info h4 {
        font-size: 16px;
    }
    
    .group-member-count {
        font-size: 13px;
    }
    
    .action-item {
        padding: 14px 15px;
    }
    
    .action-icon {
        width: 36px;
        height: 36px;
        margin-right: 12px;
    }
    
    .action-title {
        font-size: 15px;
    }
}

/* 超小屏幕适配 */
@media (max-width: 360px) {
    .group-members-grid {
        grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));
        gap: 10px;
        padding: 12px;
    }
    
    .member-item .member-avatar {
        width: 40px;
        height: 40px;
    }
    
    .member-item .member-name {
        font-size: 11px;
        max-width: 60px;
    }
    
    .selected-members {
        gap: 6px;
    }
    
    .selected-member {
        padding: 4px 8px;
        font-size: 10px;
    }
}

/* 平板设备适配 */
@media (min-width: 768px) and (max-width: 1024px) {
    .create-group-chat {
        max-width: 600px;
        margin: 0 auto;
        box-shadow: var(--shadow-lg);
        border-radius: var(--border-radius-lg);
        overflow: hidden;
    }
    
    .group-chat-detail {
        max-width: 600px;
        margin: 0 auto;
        box-shadow: var(--shadow-lg);
        border-radius: var(--border-radius-lg);
        overflow: hidden;
    }
    
    .group-members-grid {
        grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
        gap: 20px;
        padding: 25px;
    }
    
    .member-item .member-avatar {
        width: 60px;
        height: 60px;
    }
    
    .member-item .member-name {
        font-size: 14px;
        max-width: 90px;
    }
}

/* 桌面设备适配 */
@media (min-width: 1025px) {
    .create-group-chat,
    .group-chat-detail {
        max-width: 700px;
        margin: 0 auto;
        box-shadow: var(--shadow-xl);
        border-radius: var(--border-radius-lg);
        overflow: hidden;
        top: 5%;
        height: 90%;
    }
    
    .group-members-grid {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
        gap: 24px;
        padding: 30px;
    }
    
    .member-item .member-avatar {
        width: 70px;
        height: 70px;
    }
    
    .member-item .member-name {
        font-size: 15px;
        max-width: 100px;
    }
    
    .friends-list-container {
        max-height: 500px;
    }
    
    .group-info-section {
        padding: 40px 30px;
    }
    
    .group-avatar-preview {
        width: 100px;
        height: 100px;
        font-size: 40px;
    }
    
    .change-avatar-btn {
        width: 32px;
        height: 32px;
        font-size: 14px;
        bottom: -6px;
        right: -6px;
    }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
    .group-chat-btn {
        border: 2px solid white;
    }
    
    .friend-item.selected {
        background: #000;
        color: #fff;
    }
    
    .selected-member {
        border: 1px solid rgba(255, 255, 255, 0.3);
    }
    
    .action-item {
        border-bottom: 2px solid #ddd;
    }
}

/* 减弱动效模式支持 */
@media (prefers-reduced-motion: reduce) {
    .create-group-chat,
    .group-chat-detail {
        transition: none;
    }
    
    .selected-member {
        animation: none;
    }
    
    .member-item:hover {
        transform: none;
    }
    
    .group-chat-btn:hover {
        transform: translateY(-50%);
    }
    
    .confirm-btn:not(:disabled):hover {
        transform: none;
    }
    
    .add-member-btn:hover {
        transform: none;
    }
}

/* 暗色主题支持 */
@media (prefers-color-scheme: dark) {
    .create-group-chat,
    .group-chat-detail {
        background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    }
    
    .group-info-section,
    .member-selection-section,
    .group-info-card,
    .group-members-section,
    .group-actions-section {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(20px);
    }
    
    .group-chat-header,
    .group-detail-header {
        background: rgba(0, 0, 0, 0.8);
    }
    
    .friend-item:hover {
        background: rgba(255, 255, 255, 0.1);
    }
    
    .friend-item.selected {
        background: rgba(7, 193, 96, 0.2);
    }
    
    .action-item:hover {
        background: rgba(255, 255, 255, 0.05);
    }
    
    .action-item.danger-action:hover {
        background: rgba(239, 68, 68, 0.1);
    }
}

/* 无障碍优化 */
.friend-item:focus,
.member-item:focus,
.action-item:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

.group-chat-btn:focus {
    outline: 2px solid white;
    outline-offset: 2px;
}

.confirm-btn:focus,
.add-member-btn:focus {
    outline: 2px solid rgba(255, 255, 255, 0.5);
    outline-offset: 2px;
}

/* 隐藏滚动条但保持功能 */
.friends-list-container::-webkit-scrollbar,
.group-members-grid::-webkit-scrollbar {
    width: 6px;
}

.friends-list-container::-webkit-scrollbar-track,
.group-members-grid::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 3px;
}

.friends-list-container::-webkit-scrollbar-thumb,
.group-members-grid::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 3px;
}

.friends-list-container::-webkit-scrollbar-thumb:hover,
.group-members-grid::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.5);
}

/* 加载状态动画 */
@keyframes shimmer {
    0% {
        background-position: -200px 0;
    }
    100% {
        background-position: calc(200px + 100%) 0;
    }
}

.loading-shimmer {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200px 100%;
    animation: shimmer 1.5s infinite;
}

/* 触摸反馈优化 */
@media (hover: none) and (pointer: coarse) {
    .friend-item:active {
        background: rgba(7, 193, 96, 0.15);
        transform: scale(0.98);
    }
    
    .member-item:active {
        transform: scale(0.95);
    }
    
    .action-item:active {
        background: rgba(0, 0, 0, 0.1);
    }
    
    .group-chat-btn:active {
        transform: translateY(-50%) scale(0.9);
    }
}

/* 打印样式优化 */
@media print {
    .create-group-chat,
    .group-chat-detail {
        position: static;
        transform: none;
        background: white;
        box-shadow: none;
        border: 1px solid #ccc;
    }
    
    .group-chat-header,
    .group-detail-header {
        background: white;
        border-bottom: 1px solid #ccc;
    }
    
    .group-chat-btn {
        display: none;
    }
}

/* 长列表性能优化 */
.friends-list-container {
    contain: layout style paint;
}

.group-members-grid {
    contain: layout style paint;
}

/* 内容安全边距 */
.create-group-chat,
.group-chat-detail {
    padding-bottom: env(safe-area-inset-bottom);
}

@supports (padding: max(0px)) {
    .group-chat-header,
    .group-detail-header {
        padding-left: max(20px, env(safe-area-inset-left));
        padding-right: max(20px, env(safe-area-inset-right));
        padding-top: max(20px, env(safe-area-inset-top));
    }
}


