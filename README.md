# WeChat-like Web Application

一个仿微信的Web应用，包含朋友圈、聊天、相册和个人资料等功能。

## 功能特性

### 🎭 朋友圈 (Moments)
- 发布文字和图片动态
- 点赞和评论功能
- 自定义背景（支持图片和视频）
- 实时更新

### 💬 聊天 (Chat)
- 实时消息发送
- 支持文字、图片、文件
- 消息历史记录
- 用户在线状态

### 📸 相册 (Albums)
- 创建和管理相册
- 上传和浏览照片
- 相册分享功能
- 缩略图预览

### 👤 个人资料 (Profile)
- 用户信息管理
- 头像上传
- 个人设置
- 隐私控制

## 技术栈

### 前端
- **HTML5** - 页面结构
- **CSS3** - 样式设计
- **JavaScript (ES6+)** - 交互逻辑
- **响应式设计** - 移动端适配

### 后端
- **Python Flask** - Web框架
- **SQLite** - 数据库
- **RESTful API** - 接口设计

## 项目结构

```
wechat/
├── css/                    # 样式文件
│   ├── common.css         # 公共样式
│   ├── moments.css        # 朋友圈样式
│   ├── chat.css           # 聊天样式
│   ├── albums.css         # 相册样式
│   └── profile.css        # 个人资料样式
├── js/                     # JavaScript文件
│   ├── common.js          # 公共功能
│   ├── moments.js         # 朋友圈功能
│   ├── chat.js            # 聊天功能
│   ├── albums.js          # 相册功能
│   └── profile.js         # 个人资料功能
├── partials/              # HTML片段
│   ├── moments.html       # 朋友圈页面
│   ├── chat.html          # 聊天页面
│   ├── albums.html        # 相册页面
│   └── profile.html       # 个人资料页面
├── uploads/               # 上传文件目录
├── wechat/               # 数据库相关
│   ├── wechat_schema.sql  # 数据库结构
│   └── update_user_cover.sql # 背景更新脚本
├── index.html            # 主页面
├── wechat.py            # Flask后端服务
├── config.js            # 配置文件
└── package.json         # 依赖管理
```

## 安装和运行

### 1. 克隆项目
```bash
git clone https://github.com/Sini666/wechat.git
cd wechat
```

### 2. 安装依赖
```bash
# 安装Python依赖
pip install flask flask-cors

# 安装前端依赖（如果需要）
npm install
```

### 3. 初始化数据库
```bash
# 运行数据库初始化脚本
python -c "
import sqlite3
conn = sqlite3.connect('wechat.db')
with open('wechat/wechat_schema.sql', 'r', encoding='utf-8') as f:
    conn.executescript(f.read())
conn.close()
"
```

### 4. 启动服务
```bash
python wechat.py
```

### 5. 访问应用
打开浏览器访问: `http://localhost:8080`

## 主要功能说明

### 朋友圈背景设置
- 支持上传图片和视频作为背景
- 自动适配不同屏幕尺寸
- 实时预览效果

### 文件上传
- 支持多种文件格式
- 自动生成缩略图
- 安全的文件存储

### 用户认证
- 简单的用户登录系统
- 会话管理
- 权限控制

## 开发说明

### API接口
- `GET /api/user/cover` - 获取用户背景
- `POST /api/user/cover` - 设置用户背景
- `GET /api/moments` - 获取朋友圈列表
- `POST /api/moments` - 发布朋友圈
- 更多接口请查看 `wechat.py`

### 数据库表结构
- `users` - 用户信息
- `moments` - 朋友圈动态
- `comments` - 评论
- `albums` - 相册
- `messages` - 聊天消息

## 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 许可证

MIT License
