/* 我的页面样式 */
.profile-content {
    padding: 24px;
    background: transparent;
}

.profile-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-radius: var(--border-radius-xl);
    padding: 24px;
    display: flex;
    align-items: center;
    margin-bottom: 24px;
    cursor: pointer;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-fast);
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.profile-card:hover {
    transform: translateY(-5px) scale(1.02);
    box-shadow: var(--shadow-xl);
}

.profile-card::after {
    content: '\f054';
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    position: absolute;
    right: 20px;
    color: var(--dark-grey);
    font-size: 14px;
    transition: all var(--transition-fast);
}

.profile-card:hover::after {
    transform: translateX(4px);
    color: var(--primary-color);
}

.profile-avatar {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    background-color: #eee;
    background-size: cover;
    background-position: center;
    margin-right: 20px;
    border: 3px solid #fff;
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-fast);
}

.profile-card:hover .profile-avatar {
    box-shadow: var(--shadow-md);
    transform: scale(1.05);
}

.profile-info-card {
    flex: 1;
}

.profile-name {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 6px;
    color: var(--text-primary);
}

.profile-wechat-id {
    font-size: 14px;
    color: var(--dark-grey);
    display: flex;
    align-items: center;
}

.profile-wechat-id i {
    margin-right: 5px;
    font-size: 12px;
    color: var(--primary-color);
}

.profile-qrcode {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-secondary);
    font-size: 24px;
    transition: all var(--transition-fast);
}

.profile-card:hover .profile-qrcode {
    color: var(--primary-color);
}

.profile-menu {
    margin-top: 20px;
}

.menu-group {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-radius: var(--border-radius-xl);
    margin-bottom: 24px;
    box-shadow: var(--shadow-md);
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.menu-item {
    display: flex;
    align-items: center;
    padding: 18px 24px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    cursor: pointer;
    transition: all var(--transition-fast);
    position: relative;
}

.menu-item:last-child {
    border-bottom: none;
}

.menu-item i:first-child {
    margin-right: 18px;
    font-size: 22px;
    color: var(--primary-color);
    width: 28px;
    text-align: center;
    transition: all var(--transition-fast);
    filter: drop-shadow(0 2px 4px rgba(7, 193, 96, 0.2));
}

.menu-item span {
    flex: 1;
    font-size: 17px;
    font-weight: 500;
    color: var(--text-primary);
    transition: all var(--transition-fast);
}

.menu-item i:last-child {
    color: var(--dark-grey);
    transition: all var(--transition-fast);
}

.menu-item:hover {
    background: rgba(7, 193, 96, 0.05);
    transform: translateX(4px);
}

.menu-item:hover i:first-child {
    transform: scale(1.15);
    color: var(--primary-hover);
}

.menu-item:hover i:last-child {
    transform: translateX(6px);
    color: var(--primary-color);
}

/* 退出菜单项特殊样式 */
#logout-menu-item {
    color: var(--red);
}

#logout-menu-item i:first-child {
    color: var(--red);
    filter: drop-shadow(0 2px 4px rgba(220, 53, 69, 0.2));
}

#logout-menu-item span {
    color: var(--red);
}

#logout-menu-item:hover {
    background: rgba(220, 53, 69, 0.05);
}

#logout-menu-item:hover i:first-child {
    color: #dc3545;
    transform: scale(1.15);
}

#logout-menu-item:hover i:last-child {
    color: var(--red);
}

/* 个人信息编辑弹窗 */
.profile-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        radial-gradient(circle at center, rgba(0, 0, 0, 0.4) 0%, rgba(0, 0, 0, 0.7) 100%);
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 200;
    opacity: 0;
    transition: all var(--transition-normal);
}

.profile-container {
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    width: 90%;
    max-width: 420px;
    border-radius: var(--border-radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-xl);
    border: 1px solid rgba(255, 255, 255, 0.3);
    transform: translateY(30px) scale(0.9);
    transition: all var(--transition-normal);
}

.profile-modal.active {
    opacity: 1;
}

.profile-modal.active .profile-container {
    transform: translateY(0) scale(1);
}

.profile-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.profile-header h3 {
    font-size: 20px;
    font-weight: 700;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.close-btn {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    font-size: 18px;
    cursor: pointer;
    color: var(--dark-grey);
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all var(--transition-fast);
    box-shadow: var(--shadow-sm);
}

.close-btn:hover {
    background: rgba(220, 53, 69, 0.1);
    color: var(--red);
    transform: scale(1.05);
    box-shadow: var(--shadow-md);
}

.profile-form {
    padding: 24px;
}

.profile-avatar-edit {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 24px;
}

.current-avatar {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background-size: cover;
    background-position: center;
    margin-bottom: 16px;
    border: 3px solid #fff;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-fast);
    position: relative;
}

.current-avatar:hover {
    transform: scale(1.05);
}

.current-avatar::after {
    content: '\f030';
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(0, 0, 0, 0.5);
    color: #fff;
    font-size: 24px;
    opacity: 0;
    transition: all var(--transition-fast);
    border-radius: 50%;
}

.current-avatar:hover::after {
    opacity: 1;
}

.avatar-upload-btn {
    padding: 12px 20px;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-radius: var(--border-radius-lg);
    cursor: pointer;
    font-size: 15px;
    font-weight: 500;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    transition: all var(--transition-fast);
    box-shadow: var(--shadow-sm);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.avatar-upload-btn i {
    margin-right: 10px;
    color: var(--primary-color);
    font-size: 16px;
}

.avatar-upload-btn:hover {
    background: rgba(7, 193, 96, 0.1);
    transform: translateY(-3px) scale(1.02);
    box-shadow: var(--shadow-md);
    color: var(--primary-color);
}

.profile-name-edit {
    margin-bottom: 28px;
}

.profile-name-edit label {
    display: block;
    margin-bottom: 10px;
    font-size: 15px;
    color: var(--text-secondary);
    font-weight: 600;
    letter-spacing: 0.3px;
}

#profile-name {
    width: 100%;
    padding: 16px 20px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: var(--border-radius-lg);
    font-size: 16px;
    transition: all var(--transition-fast);
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    box-shadow: var(--shadow-sm);
}

#profile-name:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: var(--shadow-md), 0 0 0 3px rgba(7, 193, 96, 0.1);
    background: rgba(255, 255, 255, 0.95);
    transform: translateY(-2px);
}

.save-profile-btn {
    width: 100%;
    padding: 16px;
    background: var(--primary-gradient);
    color: #fff;
    border: none;
    border-radius: var(--border-radius-lg);
    font-size: 17px;
    font-weight: 600;
    cursor: pointer;
    transition: all var(--transition-normal);
    box-shadow: var(--shadow-md), 0 0 20px rgba(7, 193, 96, 0.3);
    letter-spacing: 0.5px;
}

.save-profile-btn:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow: var(--shadow-lg), 0 0 30px rgba(7, 193, 96, 0.4);
}

.save-profile-btn:active {
    transform: translateY(-1px) scale(0.98);
}

/* 退出登录加载弹窗 - 大厂风格设计 */
.logout-loading-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.75);
    backdrop-filter: blur(24px) saturate(180%);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 2000;
    opacity: 0;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    animation: fadeInBackdrop 0.4s ease-out forwards;
}

@keyframes fadeInBackdrop {
    from {
        opacity: 0;
        backdrop-filter: blur(0px) saturate(100%);
    }
    to {
        opacity: 1;
        backdrop-filter: blur(24px) saturate(180%);
    }
}

.logout-loading-modal.active {
    display: flex;
    opacity: 1;
}

.logout-loading-container {
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(32px) saturate(200%);
    border-radius: 28px;
    width: 90%;
    max-width: 420px;
    box-shadow:
        0 32px 80px rgba(0, 0, 0, 0.25),
        0 0 0 1px rgba(255, 255, 255, 0.5),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
    transform: translateY(40px) scale(0.9);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.6);
}

.logout-loading-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.1) 0%,
        rgba(255, 255, 255, 0.05) 50%,
        rgba(0, 0, 0, 0.02) 100%);
    z-index: -1;
}

.logout-loading-modal.active .logout-loading-container {
    transform: translateY(0) scale(1);
}

.logout-loading-content {
    text-align: center;
    padding: 48px 32px 40px;
    position: relative;
}

.logout-loading-icon {
    position: relative;
    margin-bottom: 32px;
    display: inline-block;
}

.logout-loading-icon i {
    font-size: 72px;
    background: linear-gradient(135deg, #07c160 0%, #06a84f 50%, #059142 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    filter: drop-shadow(0 8px 16px rgba(7, 193, 96, 0.3));
    animation: pulseLogout 2s ease-in-out infinite;
    position: relative;
}

@keyframes pulseLogout {
    0%, 100% {
        transform: scale(1);
        filter: drop-shadow(0 8px 16px rgba(7, 193, 96, 0.3));
    }
    50% {
        transform: scale(1.05);
        filter: drop-shadow(0 12px 24px rgba(7, 193, 96, 0.4));
    }
}

.logout-loading-title {
    font-size: 28px;
    font-weight: 700;
    color: #1a1a1a;
    margin-bottom: 16px;
    letter-spacing: -0.5px;
    line-height: 1.2;
}

.logout-loading-message {
    font-size: 18px;
    color: #666;
    line-height: 1.6;
    margin-bottom: 32px;
    font-weight: 400;
}

.logout-loading-spinner {
    display: flex;
    justify-content: center;
    align-items: center;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(7, 193, 96, 0.2);
    border-top: 4px solid #07c160;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 退出登录加载弹窗响应式设计 */
@media (max-width: 768px) {
    .logout-loading-container {
        width: 95%;
        max-width: 360px;
        margin: 20px;
        border-radius: 24px;
    }

    .logout-loading-content {
        padding: 40px 24px 32px;
    }

    .logout-loading-icon i {
        font-size: 64px;
    }

    .logout-loading-title {
        font-size: 24px;
        margin-bottom: 12px;
    }

    .logout-loading-message {
        font-size: 16px;
        margin-bottom: 24px;
    }

    .spinner {
        width: 36px;
        height: 36px;
        border-width: 3px;
    }
}