// 下拉刷新实例
let profilePullToRefresh = null;
// 防止重复初始化的标志
let profileInitialized = false;

// 初始化"我"的页面
function initProfile() {
    // 防止重复初始化
    if (profileInitialized) {
        return;
    }
    profileInitialized = true;
    // 渲染用户信息
    renderUserInfo();

    // 初始化下拉刷新功能
    initProfilePullToRefresh();

    // 个人信息卡点击事件 - 打开编辑弹窗
    const profileCard = document.getElementById('profile-card');
    if (profileCard) {
        profileCard.addEventListener('click', showProfileModal);
    }
    
    // 关闭编辑弹窗按钮
    const closeProfile = document.getElementById('close-profile');
    if (closeProfile) {
        closeProfile.addEventListener('click', hideProfileModal);
    }
    
    // 上传头像事件
    const avatarUpload = document.getElementById('avatar-upload');
    if (avatarUpload) {
        avatarUpload.addEventListener('change', handleAvatarUpload);
    }
    
    // 保存个人信息按钮
    const saveProfile = document.getElementById('save-profile');
    if (saveProfile) {
        saveProfile.addEventListener('click', saveProfileHandler);
    }
    
    // 菜单项点击事件
    const menuItems = document.querySelectorAll('.menu-item');
    if (menuItems) {
        menuItems.forEach(item => {
            item.addEventListener('click', () => {
                const feature = item.querySelector('span').textContent;
                if (feature === '相册') {
                    showPage('albums');
                } else if (feature === '退出') {
                    handleLogout();
                } else {
                    alert(`${feature}功能正在开发中，敬请期待！`);
                }
            });
        });
    }
    
    // 点击空白区域关闭弹窗
    const profileModal = document.getElementById('profile-modal');
    if (profileModal) {
        profileModal.addEventListener('click', (e) => {
            if (e.target === profileModal) {
                hideProfileModal();
            }
        });
    }

    // 初始化退出登录加载弹窗事件监听
    initLogoutLoadingModal();
}

// 渲染用户信息
function renderUserInfo() {
    // 获取最新的用户信息
    const user = getCurrentUser();

    const profileAvatar = document.querySelector('.profile-avatar');
    const profileName = document.querySelector('.profile-name');
    const wechatId = document.getElementById('wechat-id');

    if (profileAvatar && profileName && wechatId) {
        profileAvatar.style.backgroundImage = `url(${user.avatar})`;
        profileName.textContent = user.name;
        wechatId.textContent = user.wechatId;
    }

    // 编辑弹窗的用户信息
    const currentAvatar = document.getElementById('current-avatar');
    const profileNameInput = document.getElementById('profile-name');

    if (currentAvatar && profileNameInput) {
        currentAvatar.style.backgroundImage = `url(${user.avatar})`;
        profileNameInput.value = user.name;
    }
}

// 显示个人信息编辑弹窗
function showProfileModal() {
    const profileModal = document.getElementById('profile-modal');
    if (profileModal) {
        // 重新渲染用户信息到弹窗
        renderUserInfo();

        profileModal.style.display = 'flex';
        // 添加动画效果
        setTimeout(() => {
            profileModal.classList.add('active');
        }, 10);
    }
}

// 隐藏个人信息编辑弹窗
function hideProfileModal() {
    const profileModal = document.getElementById('profile-modal');
    if (profileModal) {
        profileModal.classList.remove('active');
        setTimeout(() => {
            profileModal.style.display = 'none';
        }, 300);
    }
}

// 处理头像上传
function handleAvatarUpload(event) {
    const file = event.target.files[0];
    
    if (!file) return;
    
    const reader = new FileReader();
    
    reader.onload = function(e) {
        // 临时显示上传的头像
        const currentAvatar = document.getElementById('current-avatar');
        if (currentAvatar) {
            currentAvatar.style.backgroundImage = `url(${e.target.result})`;
        }
        
        // 保存新头像的数据URL
        tempAvatarDataUrl = e.target.result;
    };
    
    reader.readAsDataURL(file);
}

// 临时存储头像数据
let tempAvatarDataUrl = null;

// 保存个人信息
function saveProfileHandler() {
    const profileNameInput = document.getElementById('profile-name');
    const saveBtn = document.getElementById('save-profile');

    if (!profileNameInput) return;

    const newName = profileNameInput.value.trim();

    if (!newName) {
        showToast('请输入用户名', 'error');
        return;
    }

    // 显示加载状态
    if (saveBtn) {
        showLoading(saveBtn, '保存中...');
    }

    // 准备要发送的数据
    const updateData = {
        username: newName
    };

    // 如果有新上传的头像，添加到数据中
    if (tempAvatarDataUrl) {
        updateData.avatar = tempAvatarDataUrl;
    }

    // 调用后端API更新用户信息
    fetch('/api/profile/update', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        credentials: 'include',
        body: JSON.stringify(updateData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 0) {
            // 更新本地存储的用户信息
            localStorage.setItem('wechat_user', JSON.stringify(data.user));

            // 更新全局用户信息
            updateCurrentUser();

            // 重新渲染用户信息
            renderUserInfo();

            // 清空临时头像数据
            tempAvatarDataUrl = null;

            // 隐藏弹窗
            hideProfileModal();

            // 显示保存成功提示
            showToast('个人信息保存成功', 'success');
        } else {
            showToast(data.msg || '保存失败', 'error');
        }
    })
    .catch(error => {
        console.error('保存用户信息失败:', error);
        showToast('网络错误，保存失败', 'error');
    })
    .finally(() => {
        // 隐藏加载状态
        if (saveBtn) {
            hideLoading(saveBtn);
        }
    });
}

// 退出登录处理 - 显示加载动画
function handleLogout() {
    showLogoutLoadingModal();
}

// 显示退出登录加载弹窗
function showLogoutLoadingModal() {
    const modal = document.getElementById('logout-loading-modal');
    if (modal) {
        modal.style.display = 'flex';
        // 强制重排
        modal.offsetHeight;
        setTimeout(() => {
            modal.classList.add('active');
        }, 10);

        // 2秒后执行退出登录
        setTimeout(() => {
            performLogout();
        }, 2000);
    }
}

// 隐藏退出登录加载弹窗
function hideLogoutLoadingModal() {
    const modal = document.getElementById('logout-loading-modal');
    if (modal) {
        modal.classList.remove('active');
        setTimeout(() => {
            modal.style.display = 'none';
        }, 400);
    }
}

// 执行退出登录
function performLogout() {
    // 隐藏加载弹窗
    hideLogoutLoadingModal();

    // 断开WebSocket连接
    if (window.wsManager) {
        window.wsManager.disconnect();
    }

    // 清除本地存储的用户信息
    localStorage.removeItem('wechat_user');

    // 清除全局用户信息
    window.currentUser = null;

    // 显示退出成功提示
    showToast('已退出登录', 'success');

    // 给WebSocket断开事件更多时间传播，然后刷新页面
    setTimeout(() => {
        window.location.reload();
    }, 1000); // 减少到1秒，足够传播断开事件
}

// 初始化退出登录加载弹窗事件监听
function initLogoutLoadingModal() {
    const modal = document.getElementById('logout-loading-modal');
    if (modal) {
        // 阻止弹窗内容区域的点击事件冒泡
        const container = modal.querySelector('.logout-loading-container');
        if (container) {
            container.addEventListener('click', (e) => {
                e.stopPropagation();
            });
        }

        // 加载弹窗不允许通过点击背景或ESC键关闭，确保退出流程完整执行
    }
}

// 初始化我页面下拉刷新功能
function initProfilePullToRefresh() {
    const profilePage = document.getElementById('profile-page');
    if (!profilePage || !window.PullToRefresh) {
        return;
    }

    // 销毁之前的实例
    if (profilePullToRefresh) {
        profilePullToRefresh.destroy();
    }

    // 创建新的下拉刷新实例
    profilePullToRefresh = new PullToRefresh(profilePage, {
        threshold: 80,
        maxDistance: 120,
        resistance: 2.5,
        refreshText: '下拉刷新个人信息',
        releaseText: '松开刷新个人信息',
        loadingText: '正在刷新个人信息...',
        completeText: '个人信息刷新完成',
        onRefresh: () => {
            return new Promise((resolve) => {
                // 重新获取用户信息
                fetch('/api/profile', {
                    credentials: 'include'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.code === 0) {
                        // 更新本地存储的用户信息
                        localStorage.setItem('wechat_user', JSON.stringify(data.user));

                        // 更新全局用户信息
                        updateCurrentUser();

                        // 重新渲染用户信息
                        renderUserInfo();

                        console.log('个人信息刷新成功');
                    } else {
                        console.error('刷新个人信息失败:', data.msg);
                        showToast('刷新失败，请重试', 'error');
                    }
                    resolve();
                })
                .catch(error => {
                    console.error('刷新个人信息错误:', error);
                    showToast('网络错误，刷新失败', 'error');
                    resolve();
                });
            });
        }
    });
}